/**
 * Festival Family - Application Database Types
 * 
 * This file provides application-friendly type definitions based on the authoritative
 * Supabase database schema. These types bridge the gap between raw database types
 * and application requirements.
 * 
 * SINGLE SOURCE OF TRUTH for application database types.
 * 
 * @version 2.0.0
 * <AUTHOR> Family Team
 */

import type { Database } from './supabase';

// Export Tables type for compatibility
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];

// ============================================================================
// CORE TABLE TYPES - Direct mappings from database
// ============================================================================

/**
 * Core database table types - these match the actual database schema exactly
 */
export type DatabaseProfile = Database['public']['Tables']['profiles']['Row'];
export type DatabaseFestival = Database['public']['Tables']['festivals']['Row'];
export type DatabaseEvent = Database['public']['Tables']['events']['Row'];
export type DatabaseActivity = Database['public']['Tables']['activities']['Row'];
export type DatabaseAnnouncement = Database['public']['Tables']['announcements']['Row'];
export type DatabaseExternalLink = Database['public']['Tables']['external_links']['Row'];
export type DatabaseGuide = Database['public']['Tables']['guides']['Row'];
export type DatabaseTip = Database['public']['Tables']['tips']['Row'];
export type DatabaseFaq = Database['public']['Tables']['faqs']['Row'];


// Additional types for missing tables (not in current supabase.ts but exist in database)

/**
 * Group type - for group management functionality
 * Based on the groups table schema with smart formation features
 */
export interface Group {
  id: string;
  name: string;
  description?: string;
  creator_id: string;
  festival_id?: string;
  is_private: boolean;
  formation_type?: 'manual' | 'activity_based' | 'music_based' | 'hybrid' | 'spontaneous';
  max_members?: number;
  auto_accept_threshold?: number;
  activity_focus?: string[];
  music_focus?: string[];
  tags?: string[];
  is_active?: boolean;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Group Member type - for group membership management
 */
export interface GroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'admin' | 'moderator' | 'member';
  joined_at: string;
  left_at?: string;
}

export interface GroupInvitation {
  id: string;
  group_id: string;
  inviter_id: string;
  invitee_id: string;
  status?: string | null;
  message?: string | null;
  expires_at?: string | null;
  created_at: string;
  responded_at?: string | null;
  inviter?: Profile; // Joined data
}

export interface EmergencyContact {
  id: string;
  festival_id: string | null;
  contact_type: string;
  name: string;
  phone?: string | null;
  email?: string | null;
  description?: string | null;
  is_primary?: boolean | null;
  order_index?: number | null;
  is_active?: boolean | null;
  created_by?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface SafetyInformation {
  id: string;
  festival_id: string | null;
  safety_category: string;
  title: string;
  content: string;
  priority?: string | null;
  is_alert?: boolean | null;
  order_index?: number | null;
  is_active?: boolean | null;
  created_by?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface ContentItem {
  id: string;
  content_key: string;
  content_type: string;
  title: string; // Made required to match usage
  content: string;
  metadata?: any;
  is_active?: boolean | null;
  version?: number | null;
  language?: string | null;
  created_by?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface FAQ {
  id: string; // Changed from number to string to match database
  question: string;
  answer: string;
  category?: string | null;
  is_active?: boolean | null;
  order_index?: number | null;
  created_at?: string | null;
  updated_at?: string | null;
}

// ============================================================================
// INSERT/UPDATE TYPES - For database operations
// ============================================================================

export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

export type FestivalInsert = Database['public']['Tables']['festivals']['Insert'];
export type FestivalUpdate = Database['public']['Tables']['festivals']['Update'];

export type EventInsert = Database['public']['Tables']['events']['Insert'];
export type EventUpdate = Database['public']['Tables']['events']['Update'];

export type ActivityInsert = Database['public']['Tables']['activities']['Insert'];
export type ActivityUpdate = Database['public']['Tables']['activities']['Update'];

export type AnnouncementInsert = Database['public']['Tables']['announcements']['Insert'];
export type AnnouncementUpdate = Database['public']['Tables']['announcements']['Update'];

export type ExternalLinkInsert = Database['public']['Tables']['external_links']['Insert'];
export type ExternalLinkUpdate = Database['public']['Tables']['external_links']['Update'];

// ============================================================================
// APPLICATION TYPES - Enhanced types for application use
// ============================================================================

/**
 * Application Profile type with additional computed properties
 * Based on the actual database schema
 */
export interface Profile extends DatabaseProfile {
  // Database fields: id, username, email, full_name, avatar_url, website, bio, role, location, interests, created_at, updated_at
  // All database fields are included via extends
  // Additional computed properties can be added here
  display_name?: string; // Computed from username or full_name
  is_complete?: boolean; // Computed based on required fields
}

/**
 * Application Festival type
 * Based on actual database schema: id, name, description, start_date, end_date, location, website, created_at, updated_at
 */
export interface Festival extends DatabaseFestival {
  // All database fields are included via extends
  // Additional computed properties
  is_active?: boolean; // Computed based on dates
  duration_days?: number; // Computed from start/end dates
}

/**
 * Application Event type
 * Based on actual database schema: id, title, description, start_date, end_date, location, festival_id, created_at, updated_at
 */
export interface Event extends DatabaseEvent {
  // All database fields included via extends (including title field)
  // Additional computed properties
  is_upcoming?: boolean; // Computed based on dates
  festival?: Festival; // Joined festival data
}

/**
 * Application Activity type
 * Based on actual database schema
 */
export interface Activity extends DatabaseActivity {
  // Database fields: id, title, description, type, location, start_date, end_date, capacity, festival_id, parent_activity_id, created_at, updated_at
  // Additional computed properties
  is_available?: boolean; // Computed based on capacity and registrations
  festival?: Festival; // Joined festival data
  parent_activity?: Activity; // Joined parent activity data
}

/**
 * Application Announcement type
 * Based on actual database schema: id, title, content, user_id, created_at, updated_at
 * 
 * NOTE: Database does NOT have 'active', 'priority', 'target_audience' fields
 */
export interface Announcement extends DatabaseAnnouncement {
  // All database fields included via extends
  // Additional computed properties for application use
  is_recent?: boolean; // Computed based on created_at
  author?: Profile; // Joined user profile data
}

/**
 * Application External Link type
 * Based on actual database schema: id, title, url, description, category, active, created_at, updated_at
 *
 * NOTE: Database does NOT have 'type' field
 */
export interface ExternalLink extends DatabaseExternalLink {
  // All database fields included via extends
  // Additional computed properties
  is_valid?: boolean; // Computed based on URL validation
}

/**
 * Application Guide type
 * Based on actual database schema
 */
export interface Guide extends DatabaseGuide {
  // All database fields included via extends (including is_featured: boolean | null)
  // Additional computed properties can be added here
}

/**
 * Application Tip type
 * Based on actual database schema
 */
export interface Tip extends DatabaseTip {
  // All database fields included via extends
  // Additional computed properties
  is_helpful?: boolean; // Computed based on user feedback
}

/**
 * Application FAQ type
 * Based on actual database schema
 */
export interface Faq extends DatabaseFaq {
  // All database fields included via extends
  // Additional computed properties
  is_popular?: boolean; // Computed based on view count or other metrics
}



// ============================================================================
// ENUM TYPES - Application-specific enums
// ============================================================================

/**
 * User roles for the application
 * These are application-level roles, not stored directly in the database
 */
export enum UserRole {
  USER = 'USER',
  MODERATOR = 'MODERATOR',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN'
}

/**
 * Activity types
 */
export enum ActivityType {
  WORKSHOP = 'WORKSHOP',
  PERFORMANCE = 'PERFORMANCE',
  MEETUP = 'MEETUP',
  FOOD = 'FOOD',
  VENDOR = 'VENDOR',
  OTHER = 'OTHER'
}

/**
 * Event status types
 */
export enum EventStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}

/**
 * Festival status types
 */
export enum FestivalStatus {
  UPCOMING = 'UPCOMING',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// ============================================================================
// LEGACY COMPATIBILITY TYPES
// ============================================================================

/**
 * User type for backward compatibility
 * Maps to Profile for now, but can be extended
 */
export interface User extends Profile {
  // Additional user-specific properties can be added here
  // Note: email and role are already included from Profile (which extends DatabaseProfile)
  // Additional computed properties can be added here
}

/**
 * Extended Festival type with additional computed properties
 */
export interface FestivalExtended extends Festival {
  // Additional computed properties
  events?: Event[]; // Related events
  activities?: Activity[]; // Related activities
  attendee_count?: number; // Computed attendee count
  is_featured?: boolean; // Featured status
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Generic response type for API operations
 */
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
}

/**
 * Paginated response type
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: PaginationMeta;
}

// ============================================================================
// RELATIONSHIP TYPES
// ============================================================================

/**
 * Festival with related events
 */
export interface FestivalWithEvents extends Festival {
  events: Event[];
}

/**
 * Event with related activities
 */
export interface EventWithActivities extends Event {
  activities: Activity[];
}

/**
 * Activity with related data
 */
export interface ActivityWithDetails extends Activity {
  event?: Event;
  parent_activity?: Activity;
  sub_activities?: Activity[];
}

// ============================================================================
// FORM TYPES
// ============================================================================

/**
 * Profile form data
 */
export interface ProfileFormData {
  username: string;
  full_name: string;
  avatar_url?: string;
  website?: string;
}

/**
 * Festival form data
 */
export interface FestivalFormData {
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  website?: string;
}

/**
 * Event form data
 */
export interface EventFormData {
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  festival_id: string; // Changed from number to string (UUID)
}

/**
 * Activity form data
 */
export interface ActivityFormData {
  name: string;
  description: string;
  type: string;
  location: string;
  start_date: string; // Fixed: database uses start_date, not start_time
  end_date: string;   // Fixed: database uses end_date, not end_time
  duration?: number;
  capacity?: number;
  festival_id?: string; // Fixed: database uses festival_id, not event_id
  parent_activity_id?: string; // Changed from number to string (UUID)
}
