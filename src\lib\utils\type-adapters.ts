/**
 * Festival Family - Type Adapters
 * 
 * This module provides utility functions to adapt between different type formats
 * and ensure compatibility between database types and component expectations.
 * 
 * @module TypeAdapters
 * @version 2.0.0
 * <AUTHOR> Family Team
 */

import type { Event, Activity, Announcement, Festival } from '@/types'

// ============================================================================
// EVENT TYPE ADAPTERS
// ============================================================================

/**
 * Adapter for Event types to handle legacy 'title' field expectations
 * Some components may still expect 'title' instead of 'name'
 */
export interface EventWithTitle extends Event {
  title: string // Computed from 'name' field
}

/**
 * Convert Event to EventWithTitle for backward compatibility
 */
export function adaptEventForLegacyComponents(event: Event): EventWithTitle {
  return {
    ...event,
    title: event.title // Events table already has title field
  }
}

/**
 * Convert array of Events to EventWithTitle array
 */
export function adaptEventsForLegacyComponents(events: Event[]): EventWithTitle[] {
  return events.map(adaptEventForLegacyComponents)
}

// ============================================================================
// ACTIVITY TYPE ADAPTERS
// ============================================================================

/**
 * Adapter for Activity types to handle legacy field expectations
 */
export interface ActivityWithTitle extends Activity {
  title: string // Computed from 'name' field
}

/**
 * Convert Activity to ActivityWithTitle for backward compatibility
 */
export function adaptActivityForLegacyComponents(activity: Activity): ActivityWithTitle {
  return {
    ...activity,
    title: activity.title // Activities table already has title field
  }
}

// ============================================================================
// ANNOUNCEMENT TYPE ADAPTERS
// ============================================================================

/**
 * Adapter for Announcement types to handle legacy 'active' field expectations
 */
export interface AnnouncementWithActive extends Announcement {
  active: boolean // Computed from 'is_active' field
}

/**
 * Convert Announcement to AnnouncementWithActive for backward compatibility
 */
export function adaptAnnouncementForLegacyComponents(announcement: Announcement): AnnouncementWithActive {
  return {
    ...announcement,
    active: true // Default to active since is_active field doesn't exist
  }
}

/**
 * Convert array of Announcements to AnnouncementWithActive array
 */
export function adaptAnnouncementsForLegacyComponents(announcements: Announcement[]): AnnouncementWithActive[] {
  return announcements.map(adaptAnnouncementForLegacyComponents)
}

// ============================================================================
// FESTIVAL TYPE ADAPTERS
// ============================================================================

/**
 * Adapter for Festival types with computed properties
 */
export interface FestivalWithComputedProps extends Festival {
  is_active: boolean // Computed based on dates
  duration_days: number // Computed from start/end dates
  status: 'upcoming' | 'active' | 'completed' // Computed status
}

/**
 * Convert Festival to FestivalWithComputedProps
 */
export function adaptFestivalWithComputedProps(festival: Festival): FestivalWithComputedProps {
  const now = new Date()
  const startDate = new Date(festival.start_date)
  const endDate = new Date(festival.end_date)
  
  // Compute status
  let status: 'upcoming' | 'active' | 'completed'
  if (now < startDate) {
    status = 'upcoming'
  } else if (now >= startDate && now <= endDate) {
    status = 'active'
  } else {
    status = 'completed'
  }
  
  // Compute duration
  const durationMs = endDate.getTime() - startDate.getTime()
  const durationDays = Math.ceil(durationMs / (1000 * 60 * 60 * 24))
  
  return {
    ...festival,
    is_active: status === 'active',
    duration_days: durationDays,
    status
  }
}

// ============================================================================
// FORM DATA ADAPTERS
// ============================================================================

/**
 * Convert form data to database-compatible format
 */
export function adaptFormDataToDatabase<T extends Record<string, any>>(
  formData: T,
  fieldMappings: Record<string, string> = {}
): T {
  const adapted = { ...formData }
  
  // Apply field mappings
  Object.entries(fieldMappings).forEach(([formField, dbField]) => {
    if (formField in adapted) {
      (adapted as any)[dbField] = (adapted as any)[formField]
      delete adapted[formField]
    }
  })
  
  return adapted
}

/**
 * Convert database data to form-compatible format
 */
export function adaptDatabaseToFormData<T extends Record<string, any>>(
  dbData: T,
  fieldMappings: Record<string, string> = {}
): T {
  const adapted = { ...dbData }
  
  // Apply reverse field mappings
  Object.entries(fieldMappings).forEach(([formField, dbField]) => {
    if (dbField in adapted) {
      (adapted as any)[formField] = (adapted as any)[dbField]
      delete adapted[dbField]
    }
  })
  
  return adapted
}

// ============================================================================
// COMMON FIELD MAPPINGS
// ============================================================================

/**
 * Common field mappings for different entities
 */
export const FIELD_MAPPINGS = {
  event: {
    title: 'name' // Form uses 'title', database uses 'name'
  },
  activity: {
    title: 'name' // Form uses 'title', database uses 'name'
  },
  announcement: {
    active: 'is_active' // Form uses 'active', database uses 'is_active'
  }
} as const

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Safe property access with fallback
 */
export function safeGet<T, K extends keyof T>(obj: T, key: K, fallback: T[K]): T[K] {
  return obj && obj[key] !== undefined ? obj[key] : fallback
}

/**
 * Convert null/undefined to empty string for form inputs
 */
export function nullToEmptyString(value: string | null | undefined): string {
  return value ?? ''
}

/**
 * Convert empty string to null for database storage
 */
export function emptyStringToNull(value: string): string | null {
  return value.trim() === '' ? null : value
}

/**
 * Ensure array type for database fields that might be null
 */
export function ensureArray<T>(value: T[] | null | undefined): T[] {
  return value ?? []
}

/**
 * Type guard to check if an object has a specific property
 */
export function hasProperty<T extends object, K extends string>(
  obj: T,
  prop: K
): obj is T & Record<K, unknown> {
  return prop in obj
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Validate that required fields are present
 */
export function validateRequiredFields<T extends Record<string, any>>(
  obj: T,
  requiredFields: (keyof T)[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields = requiredFields.filter(field => 
    obj[field] === undefined || obj[field] === null || obj[field] === ''
  ).map(String)
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  }
}

/**
 * Clean object by removing undefined/null values
 */
export function cleanObject<T extends Record<string, any>>(obj: T): Partial<T> {
  const cleaned: Partial<T> = {}
  
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      cleaned[key as keyof T] = value
    }
  })
  
  return cleaned
}
