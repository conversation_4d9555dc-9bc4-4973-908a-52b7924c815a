/**
 * Festival Service Types
 * 
 * Types for festival management including:
 * - Festival data model
 * - CRUD operation types
 * - Query filters and responses
 */

import type { Database } from '../../../types/supabase'

export type Festival = Database['public']['Tables']['festivals']['Row']
export type FestivalInsert = Database['public']['Tables']['festivals']['Insert']
export type FestivalUpdate = Database['public']['Tables']['festivals']['Update']

export interface FestivalFilter {
  search?: string
  startDate?: Date
  endDate?: Date
  location?: string
  createdBy?: string
  limit?: number
  offset?: number
}

export interface FestivalResponse {
  festival: Festival | null
  error: Error | null
  status: 'success' | 'error'
}

export interface FestivalsResponse {
  festivals: Festival[]
  count: number
  error: Error | null
  status: 'success' | 'error'
}

export interface DeleteFestivalResponse {
  error: Error | null
  status: 'success' | 'error'
}

export interface FestivalWithImage extends Festival {
  imageUrl: string | null
}

export class FestivalError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: Record<string, unknown>
  ) {
    super(message)
    this.name = 'FestivalError'
  }
}

export type InteractionType = 'LIKE' | 'COMMENT' | 'SHARE' | 'SAVE';
