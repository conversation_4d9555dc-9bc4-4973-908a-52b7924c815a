import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calendar, MapPin, Users, Heart, Clock, Trophy, Music, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useUserParticipations, useUserFavorites } from '@/hooks/useUserInteractions';
import { useActivitiesWithDetails } from '@/hooks/useActivitiesWithDetails';
import { ActivityDetailsModal } from '@/components/activities/ActivityDetailsModal';
import { ActivityWithDetails } from '@/types/activities';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface UserActivitiesListProps {
  type: 'joined' | 'favorites';
  className?: string;
}

export const UserActivitiesList: React.FC<UserActivitiesListProps> = ({ type, className = '' }) => {
  const [selectedActivity, setSelectedActivity] = useState<ActivityWithDetails | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Get user interactions
  const { participations, loading: participationsLoading } = useUserParticipations();
  const { favorites, loading: favoritesLoading } = useUserFavorites();

  // Get all activities to match with user interactions
  const { activitiesWithDetails, isLoading: activitiesLoading } = useActivitiesWithDetails({});

  const isLoading = participationsLoading || favoritesLoading || activitiesLoading;

  // Filter activities based on user interactions
  const userActivities = React.useMemo(() => {
    if (!activitiesWithDetails) return [];

    if (type === 'joined') {
      const joinedActivityIds = participations.map(p => p.activity_id);
      return activitiesWithDetails.filter(activity => 
        joinedActivityIds.includes(activity.id || '')
      );
    } else {
      const favoriteActivityIds = favorites.map(f => f.activity_id);
      return activitiesWithDetails.filter(activity => 
        favoriteActivityIds.includes(activity.id || '')
      );
    }
  }, [activitiesWithDetails, participations, favorites, type]);

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'meetup': return Users;
      case 'workshop': return Calendar;
      case 'performance': return Music;
      case 'game': return Trophy;
      case 'social': return Heart;
      default: return Zap;
    }
  };

  const getActivityColor = (activityType: string) => {
    switch (activityType) {
      case 'meetup': return 'from-blue-500/20 to-blue-600/20';
      case 'workshop': return 'from-green-500/20 to-green-600/20';
      case 'performance': return 'from-purple-500/20 to-purple-600/20';
      case 'game': return 'from-yellow-500/20 to-yellow-600/20';
      case 'social': return 'from-pink-500/20 to-pink-600/20';
      default: return 'from-gray-500/20 to-gray-600/20';
    }
  };

  const handleActivityClick = (activity: ActivityWithDetails) => {
    setSelectedActivity(activity);
    setShowModal(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (userActivities.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-full mx-auto mb-4 flex items-center justify-center">
          {type === 'joined' ? (
            <Users className="w-8 h-8 text-purple-400" />
          ) : (
            <Heart className="w-8 h-8 text-purple-400" />
          )}
        </div>
        <h3 className="text-lg font-semibold text-white mb-2">
          {type === 'joined' ? 'No Joined Activities' : 'No Favorite Activities'}
        </h3>
        <p className="text-white/70 text-sm">
          {type === 'joined' 
            ? 'You haven\'t joined any activities yet. Browse activities to get started!'
            : 'You haven\'t favorited any activities yet. Heart activities you\'re interested in!'
          }
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {userActivities.map((activity, index) => {
          const IconComponent = getActivityIcon(activity.type);
          const colorClass = getActivityColor(activity.type);

          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 transition-all duration-300 overflow-hidden text-white cursor-pointer group">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3 mb-3">
                    <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center flex-shrink-0`}>
                      <IconComponent className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-white group-hover:text-purple-300 transition-colors line-clamp-2">
                        {activity.title}
                      </h4>
                      <Badge variant="secondary" className="bg-white/10 text-white/80 text-xs mt-1">
                        {activity.type}
                      </Badge>
                    </div>
                  </div>

                  {activity.description && (
                    <p className="text-white/70 text-sm mb-3 line-clamp-2">
                      {activity.description}
                    </p>
                  )}

                  <div className="space-y-2 mb-4">
                    {activity.start_date && (
                      <div className="flex items-center gap-2 text-white/60 text-xs">
                        <Calendar className="w-3 h-3" />
                        <span>{new Date(activity.start_date).toLocaleDateString()}</span>
                      </div>
                    )}
                    {activity.location && (
                      <div className="flex items-center gap-2 text-white/60 text-xs">
                        <MapPin className="w-3 h-3" />
                        <span className="truncate">{activity.location}</span>
                      </div>
                    )}
                    {activity.capacity && (
                      <div className="flex items-center gap-2 text-white/60 text-xs">
                        <Users className="w-3 h-3" />
                        <span>{activity.capacity} spots</span>
                      </div>
                    )}
                  </div>

                  <Button
                    onClick={() => handleActivityClick(activity)}
                    variant="outline"
                    size="sm"
                    className="w-full border-white/20 text-white hover:bg-white/10"
                  >
                    View Details
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Activity Details Modal */}
      <ActivityDetailsModal
        activity={selectedActivity}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      />
    </div>
  );
};
