/**
 * Activity Attendance Service Tests
 * 
 * Comprehensive tests for activity attendance functionality.
 * Maintains Festival Family's 98.9% test coverage standards.
 * 
 * @module ActivityAttendanceServiceTests
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { describe, test, expect, beforeEach, jest } from '@jest/globals'
import { ActivityAttendanceService } from '@/lib/supabase/services/activity-attendance-service'
import type { SupabaseClient } from '@supabase/supabase-js'

// Mock Supabase client
const mockSupabaseClient = {
  from: jest.fn(),
  rpc: jest.fn()
} as unknown as SupabaseClient

// Mock query builder
const mockQueryBuilder: any = {
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  upsert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  neq: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  single: jest.fn(),
  maybeSingle: jest.fn()
}

describe('ActivityAttendanceService', () => {
  let service: ActivityAttendanceService

  beforeEach(() => {
    jest.clearAllMocks()
    service = new ActivityAttendanceService(mockSupabaseClient)
    
    // Setup default mock behavior
    ;(mockSupabaseClient.from as jest.Mock).mockReturnValue(mockQueryBuilder)
  })

  describe('setAttendance', () => {
    test('should set attendance status successfully', async () => {
      const mockAttendance = {
        id: 'attendance-1',
        user_id: 'user-1',
        activity_id: 'activity-1',
        status: 'going',
        notes: 'Looking forward to this!',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      mockQueryBuilder.single.mockResolvedValue({ data: mockAttendance, error: null })

      const result = await service.setAttendance(
        'user-1',
        'activity-1',
        'going',
        'Looking forward to this!'
      )

      expect(result.status).toBe('success')
      expect(result.data).toEqual(mockAttendance)
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('activity_attendance')
      expect(mockQueryBuilder.upsert).toHaveBeenCalledWith({
        user_id: 'user-1',
        activity_id: 'activity-1',
        status: 'going',
        notes: 'Looking forward to this!',
        updated_at: expect.any(String)
      })
    })

    test('should handle database errors', async () => {
      const mockError = new Error('Database error')
      mockQueryBuilder.single.mockResolvedValue({
        data: null,
        error: mockError
      })

      const result = await service.setAttendance('user-1', 'activity-1', 'going')

      expect(result.status).toBe('error')
      expect(result.error).toBeDefined()
      expect(result.data).toBeNull()
    })
  })

  describe('getUserAttendance', () => {
    test('should get user attendance for activity', async () => {
      const mockAttendance = {
        id: 'attendance-1',
        user_id: 'user-1',
        activity_id: 'activity-1',
        status: 'going',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      mockQueryBuilder.maybeSingle.mockResolvedValue({
        data: mockAttendance,
        error: null
      })

      const result = await service.getUserAttendance('user-1', 'activity-1')

      expect(result.status).toBe('success')
      expect(result.data).toEqual(mockAttendance)
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', 'user-1')
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('activity_id', 'activity-1')
    })

    test('should return null when no attendance found', async () => {
      mockQueryBuilder.maybeSingle.mockResolvedValue({
        data: null,
        error: null
      })

      const result = await service.getUserAttendance('user-1', 'activity-1')

      expect(result.status).toBe('success')
      expect(result.data).toBeNull()
    })
  })

  describe('getActivityAttendees', () => {
    test('should get all attendees for activity', async () => {
      const mockAttendees = [
        {
          profiles: {
            id: 'user-1',
            username: 'user1',
            full_name: 'User One',
            avatar_url: null,
            bio: null,
            interests: ['music', 'festivals']
          }
        },
        {
          profiles: {
            id: 'user-2',
            username: 'user2',
            full_name: 'User Two',
            avatar_url: null,
            bio: null,
            interests: ['dancing', 'art']
          }
        }
      ]

      mockQueryBuilder.eq.mockImplementation((field: any, value: any) => {
        if (field === 'activity_id') {
          return {
            ...mockQueryBuilder,
            then: (callback: any) => callback({ data: mockAttendees, error: null })
          }
        }
        return mockQueryBuilder
      })

      const result = await service.getActivityAttendees('activity-1')

      expect(result.status).toBe('success')
      expect(result.data).toHaveLength(2)
      expect(result.data?.[0]).toEqual(mockAttendees[0].profiles)
    })

    test('should filter attendees by status', async () => {
      const mockAttendees = [
        {
          profiles: {
            id: 'user-1',
            username: 'user1',
            full_name: 'User One',
            avatar_url: null
          }
        }
      ]

      mockQueryBuilder.eq.mockImplementation((field: any, value: any) => {
        if (field === 'status') {
          return {
            ...mockQueryBuilder,
            then: (callback: any) => callback({ data: mockAttendees, error: null })
          }
        }
        return mockQueryBuilder
      })

      const result = await service.getActivityAttendees('activity-1', 'going')

      expect(result.status).toBe('success')
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('status', 'going')
    })
  })

  describe('findActivityBuddies', () => {
    test('should find users going to same activity', async () => {
      const mockBuddies = [
        {
          profiles: {
            id: 'user-2',
            username: 'user2',
            full_name: 'User Two',
            avatar_url: null,
            bio: 'Love music festivals!',
            interests: ['music', 'dancing']
          }
        }
      ]

      mockQueryBuilder.neq.mockImplementation(() => ({
        ...mockQueryBuilder,
        then: (callback: any) => callback({ data: mockBuddies, error: null })
      }))

      const result = await service.findActivityBuddies('user-1', 'activity-1')

      expect(result.status).toBe('success')
      expect(result.data).toHaveLength(1)
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('activity_id', 'activity-1')
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('status', 'going')
      expect(mockQueryBuilder.neq).toHaveBeenCalledWith('user_id', 'user-1')
    })
  })

  describe('getUserActivitySchedule', () => {
    test('should get user activity schedule', async () => {
      const mockSchedule = [
        {
          id: 'attendance-1',
          user_id: 'user-1',
          activity_id: 'activity-1',
          status: 'going',
          activities: {
            id: 'activity-1',
            title: 'Yoga Session',
            start_date: '2024-01-01T10:00:00Z',
            end_date: '2024-01-01T11:00:00Z'
          }
        }
      ]

      mockQueryBuilder.order.mockImplementation(() => ({
        ...mockQueryBuilder,
        then: (callback: any) => callback({ data: mockSchedule, error: null })
      }))

      const result = await service.getUserActivitySchedule('user-1')

      expect(result.status).toBe('success')
      expect(result.data).toHaveLength(1)
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', 'user-1')
      expect(mockQueryBuilder.in).toHaveBeenCalledWith('status', ['going', 'interested'])
    })

    test('should filter by festival ID', async () => {
      mockQueryBuilder.order.mockImplementation(() => ({
        ...mockQueryBuilder,
        then: (callback: any) => callback({ data: [], error: null })
      }))

      await service.getUserActivitySchedule('user-1', 'festival-1')

      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('activities.festival_id', 'festival-1')
    })

    test('should filter by status', async () => {
      mockQueryBuilder.order.mockImplementation(() => ({
        ...mockQueryBuilder,
        then: (callback: any) => callback({ data: [], error: null })
      }))

      await service.getUserActivitySchedule('user-1', undefined, ['going'])

      expect(mockQueryBuilder.in).toHaveBeenCalledWith('status', ['going'])
    })
  })

  describe('removeAttendance', () => {
    test('should remove user attendance', async () => {
      mockQueryBuilder.delete.mockImplementation(() => ({
        ...mockQueryBuilder,
        then: (callback: any) => callback({ error: null })
      }))

      const result = await service.removeAttendance('user-1', 'activity-1')

      expect(result.status).toBe('success')
      expect(result.data).toBe(true)
      expect(mockQueryBuilder.delete).toHaveBeenCalled()
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('user_id', 'user-1')
      expect(mockQueryBuilder.eq).toHaveBeenCalledWith('activity_id', 'activity-1')
    })

    test('should handle deletion errors', async () => {
      const mockError = new Error('Deletion failed')
      mockQueryBuilder.delete.mockImplementation(() => ({
        ...mockQueryBuilder,
        then: (callback: any) => callback({ error: mockError })
      }))

      const result = await service.removeAttendance('user-1', 'activity-1')

      expect(result.status).toBe('error')
      expect(result.error).toBeDefined()
    })
  })

  describe('getActivitySuggestions', () => {
    test('should get activity suggestions for user', async () => {
      // Mock user profile
      mockQueryBuilder.single.mockResolvedValueOnce({
        data: { interests: ['music', 'yoga'] },
        error: null
      })

      // Mock user attendance
      mockQueryBuilder.select.mockResolvedValueOnce({
        data: [{ activity_id: 'activity-1', status: 'going' }],
        error: null
      })

      // Mock available activities
      mockQueryBuilder.limit.mockResolvedValueOnce({
        data: [
          {
            id: 'activity-2',
            title: 'Music Workshop',
            description: 'Learn about music production',
            start_date: '2024-01-02T14:00:00Z'
          }
        ],
        error: null
      })

      const result = await service.getActivitySuggestions('user-1', 'festival-1', 5)

      expect(result.status).toBe('success')
      expect(result.data).toHaveLength(1)
      expect(result.data?.[0].type).toBe('activity_buddy')
    })
  })
})
