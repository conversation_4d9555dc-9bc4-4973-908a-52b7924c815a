/**
 * Festival Form Component
 * 
 * Admin interface for creating and editing festivals.
 * Uses festivalService for data management and Zod for validation.
 */

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useNavigate, useParams } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Calendar } from '@/components/ui/calendar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { festivalService } from '@/lib/supabase/services/festival-service'
import type { Festival } from '@/types/database'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import { format } from 'date-fns'

// Form validation schema
const festivalSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  location: z.string().min(3, 'Location must be at least 3 characters'),
  start_date: z.date(),
  end_date: z.date(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).default('DRAFT'),
  featured: z.boolean().default(false),
})

type FestivalFormData = z.infer<typeof festivalSchema>

const STATUS_OPTIONS = [
  { value: 'DRAFT', label: 'Draft', color: 'gray' },
  { value: 'PUBLISHED', label: 'Published', color: 'green' },
  { value: 'ARCHIVED', label: 'Archived', color: 'red' }
]

export default function FestivalForm() {
  const { id } = useParams()
  const navigate = useNavigate()
  const { toast } = useToast()
  const { user } = useAuth()
  
  const [loading, setLoading] = useState(false)
  const [image, setImage] = useState<File>()
  const [previewUrl, setPreviewUrl] = useState<string>('')

  const form = useForm<FestivalFormData>({
    resolver: zodResolver(festivalSchema),
    defaultValues: {
      name: '',
      description: '',
      location: '',
      start_date: new Date(),
      end_date: new Date(),
      status: 'DRAFT',
      featured: false,
    },
  })

  // Load festival data if editing
  useEffect(() => {
    if (id) {
      loadFestival(id)
    }
  }, [id])

  const loadFestival = async (festivalId: string) => {
    try {
      setLoading(true)
      const response = await festivalService.getFestival(festivalId)

      if (response.status === 'error') throw new Error(response.error?.message || 'Failed to fetch festival')

      if (response.data) {
        form.reset({
          name: response.data.name,
          description: response.data.description || '',
          location: response.data.location || '',
          start_date: response.data.start_date ? new Date(response.data.start_date) : new Date(),
          end_date: response.data.end_date ? new Date(response.data.end_date) : new Date(),
          status: response.data.status || 'DRAFT',
          featured: response.data.featured || false,
        })

        // Note: image_url field doesn't exist in current database schema
        // if (response.data.image_url) {
        //   setPreviewUrl(response.data.image_url)
        // }
      }
    } catch (error) {
      console.error('Error loading festival:', error)
      toast({
        title: 'Error',
        description: 'Failed to load festival data',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setImage(file)
      setPreviewUrl(URL.createObjectURL(file))
    }
  }

  const onSubmit = async (data: FestivalFormData) => {
    try {
      setLoading(true)

      // Convert dates to ISO timestamp strings for database
      const festivalData = {
        ...data,
        start_date: data.start_date.toISOString(),
        end_date: data.end_date.toISOString(),
        created_by: user?.id || '',
        updated_at: new Date().toISOString(),
      }

      if (id) {
        const response = await festivalService.updateFestival(
          id,
          festivalData
        )

        if (response.status === 'error') throw new Error(response.error?.message || 'Failed to update festival')
        
        toast({
          title: 'Success',
          description: 'Festival updated successfully',
        })
      } else {
        // Prepare festival data with all required fields
        const createData = {
          name: festivalData.name,
          description: festivalData.description,
          location: festivalData.location,
          start_date: festivalData.start_date,
          end_date: festivalData.end_date,
          status: festivalData.status,
          featured: festivalData.featured,
          created_by: festivalData.created_by,
          website: null, // Optional field
          image_url: null, // Optional field
        }

        console.log('Creating festival with data:', createData)
        const response = await festivalService.createFestival(createData)

        if (response.status === 'error') throw new Error(response.error?.message || 'Failed to create festival')

        toast({
          title: 'Success',
          description: `Festival ${data.status === 'PUBLISHED' ? 'published' : 'saved as draft'} successfully`,
        })
      }

      navigate('/admin/festivals')
    } catch (error) {
      console.error('Error submitting festival:', error)
      toast({
        title: 'Error',
        description: 'Failed to save festival',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">
          {id ? 'Edit Festival' : 'Create Festival'}
        </h1>
        <Badge variant={form.watch('status') === 'PUBLISHED' ? 'default' : 'secondary'}>
          {STATUS_OPTIONS.find(s => s.value === form.watch('status'))?.label}
        </Badge>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Image Upload */}
          <FormItem>
            <FormLabel>Festival Image</FormLabel>
            <FormControl>
              <div className="flex flex-col items-center gap-4">
                {previewUrl && (
                  <img
                    src={previewUrl}
                    alt="Festival preview"
                    className="w-full max-w-md rounded-lg object-cover"
                  />
                )}
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="cursor-pointer"
                />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>

          {/* Festival Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Festival Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter festival name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter festival description"
                    {...field}
                    rows={4}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location */}
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl>
                  <Input placeholder="Enter festival location" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="start_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date < new Date() || date > form.getValues('end_date')
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="end_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date</FormLabel>
                  <FormControl>
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date < form.getValues('start_date')
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Publishing & Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Publishing & Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {STATUS_OPTIONS.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="featured"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Featured Festival</FormLabel>
                        <div className="text-sm text-muted-foreground">
                          Display this festival prominently on the homepage
                        </div>
                      </div>
                      <FormControl>
                        <Input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="w-4 h-4"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/festivals')}
            >
              Cancel
            </Button>

            <div className="flex space-x-2">
              {form.watch('status') !== 'DRAFT' && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    form.setValue('status', 'DRAFT');
                    form.handleSubmit(onSubmit)();
                  }}
                  disabled={loading}
                >
                  Save as Draft
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' :
                 form.watch('status') === 'PUBLISHED' ? 'Update Festival' :
                 'Save Festival'}
              </Button>
              {form.watch('status') === 'DRAFT' && (
                <Button
                  type="button"
                  onClick={() => {
                    form.setValue('status', 'PUBLISHED');
                    form.handleSubmit(onSubmit)();
                  }}
                  disabled={loading}
                >
                  Publish Now
                </Button>
              )}
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}
