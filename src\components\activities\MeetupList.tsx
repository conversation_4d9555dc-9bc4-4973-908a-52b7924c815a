import React from 'react';
import { type ActivityItem } from '@/types';

interface MeetupListProps {
  activities: ActivityItem[];
}

const MeetupList: React.FC<MeetupListProps> = ({ activities }) => {
  return (
    <div className="grid gap-4">
      {activities.map((activity) => (
        <div key={activity.id} className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
          <h3 className="text-lg font-semibold">{activity.title}</h3>
          <p className="text-sm text-white/70 mt-2">{activity.description}</p>
        </div>
      ))}
    </div>
  );
};

export default MeetupList;
