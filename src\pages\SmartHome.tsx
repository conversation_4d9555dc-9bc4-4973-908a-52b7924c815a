import React from 'react';
import { useAuth } from '../providers/ConsolidatedAuthProvider';
import PublicLanding from './PublicLanding';
import AuthenticatedHome from './AuthenticatedHome';

/**
 * Smart Home Component
 * 
 * This component intelligently routes users to either the public marketing
 * landing page or the authenticated dashboard based on their authentication status.
 * 
 * - Unauthenticated users see the public marketing landing page
 * - Authenticated users see their personalized dashboard
 */
const SmartHome: React.FC = () => {
  const { user, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-lg font-medium">Loading Festival Family...</p>
          <p className="text-sm text-white/70 mt-2">
            Preparing your festival experience
          </p>
        </div>
      </div>
    );
  }

  // Route to appropriate home page based on authentication status
  return user ? <AuthenticatedHome /> : <PublicLanding />;
};

export default SmartHome;
