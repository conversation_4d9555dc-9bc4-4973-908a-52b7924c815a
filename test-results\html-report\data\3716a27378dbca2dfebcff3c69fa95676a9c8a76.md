# Test info

- Name: Activity Cards Interactive Functionality Audit >> should test responsive design
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:145:3

# Error details

```
Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
╔═════════════════════════════════════════════════════════════════════════╗
║ Looks like Playwright Test or Playwright was just installed or updated. ║
║ Please run the following command to download new browsers:              ║
║                                                                         ║
║     npx playwright install                                              ║
║                                                                         ║
║ <3 Playwright Team                                                      ║
╚═════════════════════════════════════════════════════════════════════════╝
```

# Test source

```ts
   45 |
   46 |       // Take screenshot after clicking
   47 |       await page.screenshot({ path: 'test-results/after-join-click.png', fullPage: true });
   48 |
   49 |       // Check for success/error messages or button state changes
   50 |       const successMessage = await page.locator('text="Joined activity", text="Successfully joined", text="Please log in"').count();
   51 |       const errorMessage = await page.locator('text="Failed", text="Error"').count();
   52 |       const buttonStateChanged = await page.locator('button:has-text("Leave"), button:has-text("Joined")').count();
   53 |       const loadingSpinner = await page.locator('.animate-spin').count();
   54 |
   55 |       console.log(`Success/login messages: ${successMessage}`);
   56 |       console.log(`Error messages: ${errorMessage}`);
   57 |       console.log(`Button state changed: ${buttonStateChanged}`);
   58 |       console.log(`Loading spinners: ${loadingSpinner}`);
   59 |     }
   60 |   });
   61 |
   62 |   test('should test Details button functionality', async ({ page }) => {
   63 |     // Look for Details buttons
   64 |     const detailsButtons = page.locator('button:has-text("Details"), button:has-text("View Details")');
   65 |     const detailsButtonCount = await detailsButtons.count();
   66 |
   67 |     console.log(`Found ${detailsButtonCount} Details buttons`);
   68 |
   69 |     if (detailsButtonCount > 0) {
   70 |       // Click the first Details button
   71 |       await detailsButtons.first().click();
   72 |
   73 |       // Wait for modal or navigation
   74 |       await page.waitForTimeout(2000);
   75 |
   76 |       // Take screenshot after clicking
   77 |       await page.screenshot({ path: 'test-results/after-details-click.png', fullPage: true });
   78 |
   79 |       // Check for modal, new page, or expanded content
   80 |       const modal = await page.locator('[role="dialog"], .modal, [data-testid="activity-modal"]').count();
   81 |       const expandedContent = await page.locator('[data-testid="activity-details"]').count();
   82 |       const modalBackdrop = await page.locator('.fixed.inset-0.bg-black\\/50').count();
   83 |       const modalContent = await page.locator('text="About This Activity", text="Details", text="Close"').count();
   84 |
   85 |       console.log(`Modals opened: ${modal}`);
   86 |       console.log(`Expanded content: ${expandedContent}`);
   87 |       console.log(`Modal backdrop: ${modalBackdrop}`);
   88 |       console.log(`Modal content elements: ${modalContent}`);
   89 |     }
   90 |   });
   91 |
   92 |   test('should test Favorites/Heart button functionality', async ({ page }) => {
   93 |     // Look for heart/favorite buttons
   94 |     const favoriteButtons = page.locator('button:has([data-lucide="heart"]), button[aria-label*="favorite"], button[aria-label*="heart"]');
   95 |     const favoriteButtonCount = await favoriteButtons.count();
   96 |     
   97 |     console.log(`Found ${favoriteButtonCount} Favorite buttons`);
   98 |     
   99 |     if (favoriteButtonCount > 0) {
  100 |       // Click the first favorite button
  101 |       await favoriteButtons.first().click();
  102 |       
  103 |       // Wait for state change
  104 |       await page.waitForTimeout(2000);
  105 |       
  106 |       // Take screenshot after clicking
  107 |       await page.screenshot({ path: 'test-results/after-favorite-click.png', fullPage: true });
  108 |       
  109 |       // Check for visual state changes (filled heart, color change, etc.)
  110 |       const filledHearts = await page.locator('[data-lucide="heart"].fill-current, .text-red-400, .text-red-500').count();
  111 |       
  112 |       console.log(`Filled hearts after click: ${filledHearts}`);
  113 |     }
  114 |   });
  115 |
  116 |   test('should verify card layout and styling', async ({ page }) => {
  117 |     // Check for proper card structure
  118 |     const cards = page.locator('.bg-white\\/5, .bg-white\\/10, [class*="card"]');
  119 |     const cardCount = await cards.count();
  120 |     
  121 |     console.log(`Found ${cardCount} styled cards`);
  122 |     
  123 |     if (cardCount > 0) {
  124 |       // Check first card structure
  125 |       const firstCard = cards.first();
  126 |       
  127 |       // Verify card has title
  128 |       const hasTitle = await firstCard.locator('h4, h3, [class*="title"], [class*="font-semibold"]').count() > 0;
  129 |       
  130 |       // Verify card has description
  131 |       const hasDescription = await firstCard.locator('p, [class*="description"]').count() > 0;
  132 |       
  133 |       // Verify card has action buttons
  134 |       const hasButtons = await firstCard.locator('button').count() > 0;
  135 |       
  136 |       console.log(`Card has title: ${hasTitle}`);
  137 |       console.log(`Card has description: ${hasDescription}`);
  138 |       console.log(`Card has buttons: ${hasButtons}`);
  139 |       
  140 |       // Take detailed screenshot of first card
  141 |       await firstCard.screenshot({ path: 'test-results/first-activity-card.png' });
  142 |     }
  143 |   });
  144 |
> 145 |   test('should test responsive design', async ({ page }) => {
      |   ^ Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
  146 |     // Test mobile viewport
  147 |     await page.setViewportSize({ width: 375, height: 667 });
  148 |     await page.waitForTimeout(1000);
  149 |     await page.screenshot({ path: 'test-results/activities-mobile.png', fullPage: true });
  150 |     
  151 |     // Test tablet viewport
  152 |     await page.setViewportSize({ width: 768, height: 1024 });
  153 |     await page.waitForTimeout(1000);
  154 |     await page.screenshot({ path: 'test-results/activities-tablet.png', fullPage: true });
  155 |     
  156 |     // Test desktop viewport
  157 |     await page.setViewportSize({ width: 1920, height: 1080 });
  158 |     await page.waitForTimeout(1000);
  159 |     await page.screenshot({ path: 'test-results/activities-desktop.png', fullPage: true });
  160 |   });
  161 | });
  162 |
```