/**
 * Music Preference Service
 * 
 * Handles music preferences, artist tracking, and music-based matching.
 * Enables community building through shared music interests.
 * 
 * @module MusicPreferenceService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from './base-service'
import type { 
  ArtistPreference,
  MusicGenrePreference,
  PreferenceLevel,
  UserMusicProfile,
  MusicMatch,
  SetMusicPreferenceRequest,
  MusicMatchingFilter
} from '../activity-coordination/types'
import type { Profile } from '../database.types'

/**
 * Service for managing music preferences and music-based matching
 */
export class MusicPreferenceService extends BaseService {
  
  /**
   * Set or update artist preference for a user
   */
  async setArtistPreference(
    userId: string,
    artistName: string,
    preferenceLevel: PreferenceLevel,
    genre?: string,
    spotifyArtistId?: string
  ): Promise<ServiceResponse<ArtistPreference>> {
    return this.handleResponse(
      this.client
        .from('artist_preferences')
        .upsert({
          user_id: userId,
          artist_name: artistName,
          preference_level: preferenceLevel,
          genre,
          spotify_artist_id: spotifyArtistId,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
    );
  }

  /**
   * Set or update genre preference for a user
   */
  async setGenrePreference(
    userId: string,
    genre: string,
    preferenceLevel: PreferenceLevel
  ): Promise<ServiceResponse<MusicGenrePreference>> {
    return this.handleResponse(
      this.client
        .from('music_genre_preferences')
        .upsert({
          user_id: userId,
          genre,
          preference_level: preferenceLevel,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
    );
  }

  /**
   * Get user's complete music profile
   */
  async getUserMusicProfile(
    userId: string
  ): Promise<ServiceResponse<UserMusicProfile>> {
    return this.handleResponse(
      (async () => {
        const [artistPrefs, genrePrefs] = await Promise.all([
          this.client
            .from('artist_preferences')
            .select('*')
            .eq('user_id', userId)
            .order('preference_level', { ascending: false }),
          this.client
            .from('music_genre_preferences')
            .select('*')
            .eq('user_id', userId)
            .order('preference_level', { ascending: false })
        ]);

        if (artistPrefs.error) throw artistPrefs.error;
        if (genrePrefs.error) throw genrePrefs.error;

        const musicProfile: UserMusicProfile = {
          user_id: userId,
          artist_preferences: artistPrefs.data || [],
          genre_preferences: genrePrefs.data || [],
          top_genres: this.extractTopGenres(genrePrefs.data || []),
          top_artists: this.extractTopArtists(artistPrefs.data || [])
        };

        return musicProfile;
      })()
    );
  }

  /**
   * Find users with similar music taste
   */
  async findMusicBuddies(
    userId: string,
    filter?: MusicMatchingFilter,
    limit = 10
  ): Promise<ServiceResponse<MusicMatch[]>> {
    return this.handleResponse(
      (async () => {
        // Use the database function for efficient matching
        const { data: matches, error } = await this.client
          .rpc('find_music_buddies', {
            target_user_id: userId,
            result_limit: limit
          });

        if (error) throw error;

        // Enhance matches with profile data and detailed analysis
        const enhancedMatches: MusicMatch[] = [];

        for (const match of matches || []) {
          // Get user profile
          const { data: profile, error: profileError } = await this.client
            .from('profiles')
            .select('id, username, full_name, avatar_url, bio, interests')
            .eq('id', match.user_id)
            .single();

          if (profileError) continue;

          // Get shared artists and genres details
          const [sharedArtists, sharedGenres] = await Promise.all([
            this.getSharedArtists(userId, match.user_id),
            this.getSharedGenres(userId, match.user_id)
          ]);

          const musicMatch: MusicMatch = {
            user: {
              ...(profile as any),
              created_at: (profile as any).created_at || new Date().toISOString(),
              role: (profile as any).role || 'user',
              updated_at: (profile as any).updated_at || new Date().toISOString()
            } as Profile,
            shared_artists: sharedArtists.data || [],
            shared_genres: sharedGenres.data || [],
            compatibility_score: Number(match.compatibility_score),
            match_strength: this.calculateMatchStrength(Number(match.compatibility_score))
          };

          enhancedMatches.push(musicMatch);
        }

        return enhancedMatches;
      })()
    );
  }

  /**
   * Get shared artists between two users
   */
  async getSharedArtists(
    userId1: string,
    userId2: string
  ): Promise<ServiceResponse<string[]>> {
    return this.handleResponse(
      (async () => {
        const { data, error } = await this.client
          .from('artist_preferences')
          .select('artist_name')
          .eq('user_id', userId1)
          .in('preference_level', ['love', 'like']);

        if (error) throw error;

        const user1Artists = data?.map(p => p.artist_name) || [];

        const { data: user2Data, error: user2Error } = await this.client
          .from('artist_preferences')
          .select('artist_name')
          .eq('user_id', userId2)
          .in('preference_level', ['love', 'like'])
          .in('artist_name', user1Artists);

        if (user2Error) throw user2Error;

        return user2Data?.map(p => p.artist_name) || [];
      })()
    );
  }

  /**
   * Get shared genres between two users
   */
  async getSharedGenres(
    userId1: string,
    userId2: string
  ): Promise<ServiceResponse<string[]>> {
    return this.handleResponse(
      (async () => {
        const { data, error } = await this.client
          .from('music_genre_preferences')
          .select('genre')
          .eq('user_id', userId1)
          .in('preference_level', ['love', 'like']);

        if (error) throw error;

        const user1Genres = data?.map(p => p.genre) || [];

        const { data: user2Data, error: user2Error } = await this.client
          .from('music_genre_preferences')
          .select('genre')
          .eq('user_id', userId2)
          .in('preference_level', ['love', 'like'])
          .in('genre', user1Genres);

        if (user2Error) throw user2Error;

        return user2Data?.map(p => p.genre) || [];
      })()
    );
  }

  /**
   * Get users who love a specific artist
   */
  async getArtistFans(
    artistName: string,
    excludeUserId?: string,
    limit = 20
  ): Promise<ServiceResponse<Profile[]>> {
    return this.handleResponse(
      (async () => {
        let query = this.client
          .from('artist_preferences')
          .select(`
            profiles!artist_preferences_user_id_fkey(
              id,
              username,
              full_name,
              avatar_url,
              bio,
              interests
            )
          `)
          .eq('artist_name', artistName)
          .in('preference_level', ['love', 'like']);

        if (excludeUserId) {
          query = query.neq('user_id', excludeUserId);
        }

        const { data, error } = await query.limit(limit);

        if (error) throw error;

        return data?.map(item => item.profiles).filter(Boolean) || [];
      })()
    );
  }

  /**
   * Get popular artists in the community
   */
  async getPopularArtists(
    limit = 20
  ): Promise<ServiceResponse<{ artist_name: string; fan_count: number }[]>> {
    return this.handleResponse(
      (async () => {
        const { data, error } = await this.client
          .from('artist_preferences')
          .select('artist_name')
          .in('preference_level', ['love', 'like']);

        if (error) throw error;

        // Count occurrences
        const artistCounts: { [key: string]: number } = {};
        data?.forEach(pref => {
          artistCounts[pref.artist_name] = (artistCounts[pref.artist_name] || 0) + 1;
        });

        // Sort by popularity
        const popularArtists = Object.entries(artistCounts)
          .map(([artist_name, fan_count]) => ({ artist_name, fan_count }))
          .sort((a, b) => b.fan_count - a.fan_count)
          .slice(0, limit);

        return popularArtists;
      })()
    );
  }

  /**
   * Remove artist preference
   */
  async removeArtistPreference(
    userId: string,
    artistName: string
  ): Promise<ServiceResponse<boolean>> {
    return this.handleResponse(
      (async () => {
        const { error } = await this.client
          .from('artist_preferences')
          .delete()
          .eq('user_id', userId)
          .eq('artist_name', artistName);

        if (error) throw error;
        return true;
      })()
    );
  }

  /**
   * Remove genre preference
   */
  async removeGenrePreference(
    userId: string,
    genre: string
  ): Promise<ServiceResponse<boolean>> {
    return this.handleResponse(
      (async () => {
        const { error } = await this.client
          .from('music_genre_preferences')
          .delete()
          .eq('user_id', userId)
          .eq('genre', genre);

        if (error) throw error;
        return true;
      })()
    );
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private extractTopGenres(preferences: MusicGenrePreference[]): string[] {
    return preferences
      .filter(p => p.preference_level === 'love')
      .map(p => p.genre)
      .slice(0, 5);
  }

  private extractTopArtists(preferences: ArtistPreference[]): string[] {
    return preferences
      .filter(p => p.preference_level === 'love')
      .map(p => p.artist_name)
      .slice(0, 10);
  }

  private calculateMatchStrength(score: number): 'high' | 'medium' | 'low' {
    if (score >= 8) return 'high';
    if (score >= 4) return 'medium';
    return 'low';
  }
}

// Export singleton instance
export const musicPreferenceService = new MusicPreferenceService();
