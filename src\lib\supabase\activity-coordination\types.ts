/**
 * Activity Coordination Types
 * 
 * Types for activity-based community building including:
 * - Activity attendance tracking
 * - Music preference system
 * - Real-time coordination features
 * - External chat links integration
 * 
 * @module ActivityCoordinationTypes
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import type { Database } from '../../types/database'
import type { Profile, Activity, Group } from '../database.types'

// ============================================================================
// DATABASE TABLE TYPES
// ============================================================================

export type AttendanceStatus = 'going' | 'interested' | 'maybe' | 'not_going';
export type PreferenceLevel = 'love' | 'like' | 'neutral' | 'dislike';
export type ChatPlatform = 'whatsapp' | 'discord' | 'telegram' | 'signal' | 'other';

// Activity Attendance
export interface ActivityAttendance {
  id: string;
  user_id: string;
  activity_id: string;
  status: AttendanceStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Artist Preferences
export interface ArtistPreference {
  id: string;
  user_id: string;
  artist_name: string;
  preference_level: PreferenceLevel;
  genre?: string;
  spotify_artist_id?: string;
  created_at: string;
  updated_at: string;
}

// Music Genre Preferences
export interface MusicGenrePreference {
  id: string;
  user_id: string;
  genre: string;
  preference_level: PreferenceLevel;
  created_at: string;
  updated_at: string;
}

// External Chat Links
export interface GroupExternalLink {
  id: string;
  group_id: string;
  platform: ChatPlatform;
  link_url: string;
  display_name: string;
  description?: string;
  created_by: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// ============================================================================
// ENHANCED ENTITY TYPES
// ============================================================================

// Activity with attendance data
export interface ActivityWithAttendance extends Activity {
  attendance?: ActivityAttendance[];
  user_attendance?: ActivityAttendance; // Current user's attendance
  attendance_counts?: {
    going: number;
    interested: number;
    maybe: number;
    total: number;
  };
  attendees?: Profile[]; // Users attending this activity
}

// User with music profile
export interface UserMusicProfile {
  user_id: string;
  artist_preferences: ArtistPreference[];
  genre_preferences: MusicGenrePreference[];
  top_genres: string[];
  top_artists: string[];
  music_compatibility_score?: number; // For matching
}

// Group with external links
export interface GroupWithExternalLinks extends Group {
  external_links?: GroupExternalLink[];
  active_external_links?: GroupExternalLink[];
}

// ============================================================================
// MATCHING & COORDINATION TYPES
// ============================================================================

// Activity-based matching
export interface ActivityMatch {
  activity: ActivityWithAttendance;
  matching_users: Profile[];
  shared_interests: string[];
  compatibility_score: number;
  match_reason: string;
}

// Music-based matching
export interface MusicMatch {
  user: Profile;
  shared_artists: string[];
  shared_genres: string[];
  compatibility_score: number;
  match_strength: 'high' | 'medium' | 'low';
}

// Real-time coordination suggestions
export interface ActivityCoordinationSuggestion {
  id: string;
  type: 'activity_buddy' | 'music_group' | 'skill_share' | 'travel_buddy' | 'spontaneous_meetup';
  title: string;
  description: string;
  activity?: ActivityWithAttendance;
  suggested_users: Profile[];
  reason: string;
  confidence_score: number;
  expires_at?: string; // For time-sensitive suggestions
  location?: string; // For location-based suggestions
}

// Live activity coordination
export interface LiveActivityStatus {
  activity_id: string;
  current_attendees: Profile[];
  looking_for_buddies: Profile[];
  forming_groups: {
    id: string;
    name: string;
    members: Profile[];
    max_size: number;
    activity_focus: string;
  }[];
  real_time_updates: {
    timestamp: string;
    type: 'user_arrived' | 'user_left' | 'group_formed' | 'buddy_request';
    data: any;
  }[];
}

// ============================================================================
// API REQUEST/RESPONSE TYPES
// ============================================================================

// Set attendance request
export interface SetAttendanceRequest {
  user_id: string;
  activity_id: string;
  status: AttendanceStatus;
  notes?: string;
}

// Music preference request
export interface SetMusicPreferenceRequest {
  user_id: string;
  artist_name?: string;
  genre?: string;
  preference_level: PreferenceLevel;
  spotify_artist_id?: string;
}

// External link request
export interface CreateExternalLinkRequest {
  group_id: string;
  platform: ChatPlatform;
  link_url: string;
  display_name: string;
  description?: string;
  created_by: string;
}

// Activity buddy search request
export interface FindActivityBuddiesRequest {
  user_id: string;
  activity_id?: string;
  festival_id?: string;
  music_preferences?: string[];
  activity_types?: string[];
  max_results?: number;
}

// ============================================================================
// FILTER & SEARCH TYPES
// ============================================================================

export interface ActivityCoordinationFilter {
  festival_id?: string;
  activity_types?: string[];
  attendance_status?: AttendanceStatus[];
  music_genres?: string[];
  date_range?: {
    start: string;
    end: string;
  };
  location_radius?: {
    lat: number;
    lng: number;
    radius_km: number;
  };
}

export interface MusicMatchingFilter {
  genres?: string[];
  artists?: string[];
  preference_levels?: PreferenceLevel[];
  compatibility_threshold?: number;
  exclude_user_ids?: string[];
}

// ============================================================================
// REAL-TIME SUBSCRIPTION TYPES
// ============================================================================

export interface ActivityCoordinationSubscription {
  channel: string;
  event_type: 'attendance_updated' | 'buddy_request' | 'group_formed' | 'activity_started';
  payload: {
    activity_id: string;
    user_id: string;
    data: any;
    timestamp: string;
  };
}

// ============================================================================
// ANALYTICS & INSIGHTS TYPES
// ============================================================================

export interface ActivityCoordinationInsights {
  user_id: string;
  total_activities_attended: number;
  favorite_activity_types: string[];
  music_compatibility_average: number;
  successful_connections: number;
  preferred_group_sizes: number[];
  peak_activity_times: string[];
  festival_participation_rate: number;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface ActivityCoordinationError {
  code: 'ATTENDANCE_CONFLICT' | 'MUSIC_PREFERENCE_INVALID' | 'EXTERNAL_LINK_INVALID' | 'COORDINATION_FAILED';
  message: string;
  details?: any;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type ActivityCoordinationEvent = 
  | { type: 'ATTENDANCE_SET'; payload: ActivityAttendance }
  | { type: 'MUSIC_PREFERENCE_SET'; payload: ArtistPreference | MusicGenrePreference }
  | { type: 'BUDDY_MATCH_FOUND'; payload: ActivityMatch }
  | { type: 'GROUP_SUGGESTION'; payload: ActivityCoordinationSuggestion }
  | { type: 'EXTERNAL_LINK_ADDED'; payload: GroupExternalLink };

export type CoordinationStatus = 'active' | 'pending' | 'completed' | 'cancelled';

export type MatchingAlgorithm = 'music_similarity' | 'activity_overlap' | 'location_proximity' | 'social_graph';

// ============================================================================
// SMART GROUP FORMATION TYPES
// ============================================================================

export type GroupFormationType = 'manual' | 'activity_based' | 'music_based' | 'hybrid' | 'spontaneous';
export type SuggestionStatus = 'pending' | 'accepted' | 'declined' | 'expired';

// Group suggestion
export interface GroupSuggestion {
  id: string;
  suggested_name: string;
  suggested_description?: string;
  formation_type: GroupFormationType;
  festival_id: string;
  activity_focus?: string[];
  music_focus?: string[];
  target_users: string[];
  creator_id: string;
  min_members: number;
  max_members: number;
  status: SuggestionStatus;
  confidence_score: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

// Group suggestion response
export interface GroupSuggestionResponse {
  id: string;
  suggestion_id: string;
  user_id: string;
  response: boolean;
  response_notes?: string;
  responded_at: string;
  formed_group_id?: string; // Set if group was auto-formed
}

// Enhanced group with formation data
export interface SmartGroup extends Group {
  formation_type?: GroupFormationType;
  max_members?: number;
  auto_accept_threshold?: number;
  activity_focus?: string[];
  music_focus?: string[];
  tags?: string[];
  is_active?: boolean;
  expires_at?: string;
}

// Activity-based group candidate
export interface ActivityBasedGroupCandidate {
  user_id: string;
  shared_activities: string[];
  shared_activity_count: number;
  compatibility_score: number;
}

// Music-based group candidate
export interface MusicBasedGroupCandidate {
  user_id: string;
  shared_artists_count: number;
  shared_genres_count: number;
  compatibility_score: number;
}

// Smart group formation request
export interface SmartGroupFormationRequest {
  creator_id: string;
  festival_id: string;
  formation_type: GroupFormationType;
  suggested_name?: string;
  suggested_description?: string;
  activity_focus?: string[];
  music_focus?: string[];
  min_members?: number;
  max_members?: number;
  confidence_threshold?: number;
}

// Group formation insights
export interface GroupFormationInsights {
  user_id: string;
  festival_id: string;
  activity_interests: string[];
  music_interests: string[];
  pending_suggestions: number;
  formation_potential: number; // 0-1 score
  recommended_actions: string[];
}
