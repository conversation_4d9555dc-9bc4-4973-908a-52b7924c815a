import React from 'react';
import { motion } from 'framer-motion';
import { Users, Heart, Calendar, Trophy, TrendingUp, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { useUserParticipations, useUserFavorites } from '@/hooks/useUserInteractions';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface StatCardProps {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  color: string;
  index: number;
}

const StatCard: React.FC<StatCardProps> = ({ icon, label, value, color, index }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.3, delay: index * 0.1 }}
  >
    <Card className="bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 transition-all duration-300 text-white">
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 bg-gradient-to-br ${color} rounded-lg flex items-center justify-center`}>
            {icon}
          </div>
          <div>
            <p className="text-2xl font-bold text-white">{value}</p>
            <p className="text-white/70 text-sm">{label}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  </motion.div>
);

export const UserStats: React.FC = () => {
  const { participations, loading: participationsLoading } = useUserParticipations();
  const { favorites, loading: favoritesLoading } = useUserFavorites();

  const isLoading = participationsLoading || favoritesLoading;

  // Calculate statistics
  const stats = React.useMemo(() => {
    const totalJoined = participations.length;
    const totalFavorites = favorites.length;
    
    // Calculate activities by status
    const attendedCount = participations.filter((p: any) => p.status === 'attended').length;
    const registeredCount = participations.filter((p: any) => p.status === 'registered').length;
    
    // Calculate recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentJoined = participations.filter((p: any) =>
      new Date(p.created_at) > thirtyDaysAgo
    ).length;

    const recentFavorites = favorites.filter((f: any) =>
      new Date(f.created_at) > thirtyDaysAgo
    ).length;

    return {
      totalJoined,
      totalFavorites,
      attendedCount,
      registeredCount,
      recentActivity: recentJoined + recentFavorites,
      engagementRate: totalJoined > 0 ? Math.round((attendedCount / totalJoined) * 100) : 0
    };
  }, [participations, favorites]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  const statCards = [
    {
      icon: <Users className="w-5 h-5 text-blue-400" />,
      label: 'Activities Joined',
      value: stats.totalJoined,
      color: 'from-blue-500/20 to-blue-600/20'
    },
    {
      icon: <Heart className="w-5 h-5 text-red-400" />,
      label: 'Favorites',
      value: stats.totalFavorites,
      color: 'from-red-500/20 to-red-600/20'
    },
    {
      icon: <Trophy className="w-5 h-5 text-yellow-400" />,
      label: 'Attended',
      value: stats.attendedCount,
      color: 'from-yellow-500/20 to-yellow-600/20'
    },
    {
      icon: <Calendar className="w-5 h-5 text-green-400" />,
      label: 'Upcoming',
      value: stats.registeredCount,
      color: 'from-green-500/20 to-green-600/20'
    },
    {
      icon: <TrendingUp className="w-5 h-5 text-purple-400" />,
      label: 'Recent Activity',
      value: stats.recentActivity,
      color: 'from-purple-500/20 to-purple-600/20'
    },
    {
      icon: <Clock className="w-5 h-5 text-orange-400" />,
      label: 'Engagement Rate',
      value: `${stats.engagementRate}%`,
      color: 'from-orange-500/20 to-orange-600/20'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {statCards.map((stat, index) => (
          <StatCard
            key={stat.label}
            icon={stat.icon}
            label={stat.label}
            value={stat.value}
            color={stat.color}
            index={index}
          />
        ))}
      </div>

      {/* Additional Insights */}
      {stats.totalJoined > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          <Card className="bg-white/5 border border-white/10 text-white">
            <CardContent className="p-4">
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-purple-400" />
                Activity Insights
              </h3>
              <div className="space-y-2 text-sm text-white/70">
                <p>
                  • You've joined <span className="text-white font-medium">{stats.totalJoined}</span> activities
                  {stats.attendedCount > 0 && (
                    <span> and attended <span className="text-green-400 font-medium">{stats.attendedCount}</span> of them</span>
                  )}
                </p>
                <p>
                  • You have <span className="text-white font-medium">{stats.totalFavorites}</span> favorite activities
                </p>
                {stats.recentActivity > 0 && (
                  <p>
                    • <span className="text-purple-400 font-medium">{stats.recentActivity}</span> activities added in the last 30 days
                  </p>
                )}
                {stats.engagementRate > 0 && (
                  <p>
                    • Your engagement rate is <span className="text-orange-400 font-medium">{stats.engagementRate}%</span>
                    {stats.engagementRate >= 80 && <span className="text-green-400"> - Excellent!</span>}
                    {stats.engagementRate >= 60 && stats.engagementRate < 80 && <span className="text-yellow-400"> - Good!</span>}
                    {stats.engagementRate < 60 && <span className="text-orange-400"> - Room for improvement</span>}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
};
