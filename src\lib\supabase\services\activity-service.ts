/**
 * Festival Family - Activity Service
 *
 * This service handles all activity-related operations with proper type safety
 * and error handling. It provides comprehensive activity management functionality.
 *
 * @module ActivityService
 * @version 2.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from './base-service'
import type { Activity, ActivityWithDetails } from '../../../types/database'

export class ActivityService extends BaseService {
  /**
   * Get all activities
   */
  async getActivities(): Promise<ServiceResponse<Activity[]>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .order('start_time', { ascending: true })
    )
  }

  /**
   * Get an activity by ID
   */
  async getActivity(id: number): Promise<ServiceResponse<Activity>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .eq('id', id)
        .single()
    )
  }

  /**
   * Get activity with related details (event, parent activity, sub-activities)
   */
  async getActivityWithDetails(id: number): Promise<ServiceResponse<ActivityWithDetails>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select(`
          *,
          event:events(*),
          parent_activity:activities!parent_activity_id(*),
          sub_activities:activities!parent_activity_id(*)
        `)
        .eq('id', id)
        .single()
    )
  }

  /**
   * Get activities by event ID
   */
  async getActivitiesByEvent(eventId: number): Promise<ServiceResponse<Activity[]>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .eq('event_id', eventId)
        .order('start_time', { ascending: true })
    )
  }

  /**
   * Get activities by type
   */
  async getActivitiesByType(type: string): Promise<ServiceResponse<Activity[]>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .eq('type', type)
        .order('start_date', { ascending: true })
    )
  }

  /**
   * Get activities by time range
   */
  async getActivitiesByTimeRange(startTime: string, endTime: string): Promise<ServiceResponse<Activity[]>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .gte('start_date', startTime)
        .lte('end_date', endTime)
        .order('start_date', { ascending: true })
    )
  }

  /**
   * Search activities by name or description
   */
  async searchActivities(query: string): Promise<ServiceResponse<Activity[]>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .order('start_time', { ascending: true })
    )
  }

  /**
   * Create a new activity
   */
  async createActivity(activityData: Omit<Activity, 'id' | 'created_at' | 'updated_at'>): Promise<ServiceResponse<Activity>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .insert(activityData)
        .select()
        .single()
    )
  }

  /**
   * Update an activity
   */
  async updateActivity(id: number, updates: Partial<Activity>): Promise<ServiceResponse<Activity>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .update(updates)
        .eq('id', id)
        .select()
        .single()
    )
  }

  /**
   * Delete an activity
   */
  async deleteActivity(id: number): Promise<ServiceResponse<null>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .delete()
        .eq('id', id)
    )
  }

  /**
   * Get sub-activities for a parent activity
   */
  async getSubActivities(parentActivityId: number): Promise<ServiceResponse<Activity[]>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .eq('parent_activity_id', parentActivityId)
        .order('start_time', { ascending: true })
    )
  }

  /**
   * Get activities with available capacity
   */
  async getAvailableActivities(): Promise<ServiceResponse<Activity[]>> {
    return this.handleResponse(
      this.client
        .from('activities')
        .select('*')
        .or('capacity.is.null,capacity.gt.0')
        .order('start_time', { ascending: true })
    )
  }
}

// Export a singleton instance
export const activityService = new ActivityService()
