{"config": {"configFile": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\playwright.config.js", "rootDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null], ["junit", {"outputFile": "test-results/junit.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/CascadeProjects/festival-family/test-results/artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/CascadeProjects/festival-family/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "npm run dev", "port": 5173, "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "activity-cards-audit.spec.ts", "file": "activity-cards-audit.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Activity Cards Interactive Functionality Audit", "file": "activity-cards-audit.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display activity cards with real database data", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 25275, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:12.146Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-5e99074e1b7d2e7fdbea", "file": "activity-cards-audit.spec.ts", "line": 15, "column": 3}, {"title": "should test Join Activity button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "failed", "duration": 27262, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:55.496Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-b7630765653fb9c0ca8c", "file": "activity-cards-audit.spec.ts", "line": 32, "column": 3}, {"title": "should test Details button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "failed", "duration": 26739, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:50:34.440Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-446dbfb32a74e8930789", "file": "activity-cards-audit.spec.ts", "line": 62, "column": 3}, {"title": "should test Favorites/Heart button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 23, "parallelIndex": 0, "status": "failed", "duration": 24042, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:13.161Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-0c20e689f67c965914db", "file": "activity-cards-audit.spec.ts", "line": 92, "column": 3}, {"title": "should verify card layout and styling", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 28, "parallelIndex": 0, "status": "failed", "duration": 24500, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:43.337Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-chromium\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-d57ac2b5c0aa2d66050b", "file": "activity-cards-audit.spec.ts", "line": 116, "column": 3}, {"title": "should test responsive design", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 33, "parallelIndex": 0, "status": "interrupted", "duration": 10188, "error": {"message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:52:22.487Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-chromium\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "018dd493fd601623e406-d8c1eb7f1c97c60b5399", "file": "activity-cards-audit.spec.ts", "line": 145, "column": 3}, {"title": "should display activity cards with real database data", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 19, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:12.133Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "018dd493fd601623e406-6368a7e86ddb3fb87813", "file": "activity-cards-audit.spec.ts", "line": 15, "column": 3}, {"title": "should test Join Activity button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "failed", "duration": 18, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:14.600Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "018dd493fd601623e406-c5c7795bbd18f0f71b65", "file": "activity-cards-audit.spec.ts", "line": 32, "column": 3}, {"title": "should test Details button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "failed", "duration": 51, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:19.632Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "018dd493fd601623e406-02a0c148fe7ff2a1b06a", "file": "activity-cards-audit.spec.ts", "line": 62, "column": 3}, {"title": "should test Favorites/Heart button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "failed", "duration": 50, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:27.063Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "018dd493fd601623e406-242649973584e210501b", "file": "activity-cards-audit.spec.ts", "line": 92, "column": 3}, {"title": "should verify card layout and styling", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "failed", "duration": 23, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:54.925Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "018dd493fd601623e406-22e2bd8300e57358ba19", "file": "activity-cards-audit.spec.ts", "line": 116, "column": 3}, {"title": "should test responsive design", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 1, "status": "failed", "duration": 38, "error": {"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝", "stack": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}, "errors": [{"message": "Error: browserType.launch: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\firefox-1482\\firefox\\firefox.exe\n╔═════════════════════════════════════════════════════════════════════════╗\n║ Looks like Playwright Test or Playwright was just installed or updated. ║\n║ Please run the following command to download new browsers:              ║\n║                                                                         ║\n║     npx playwright install                                              ║\n║                                                                         ║\n║ <3 Playwright Team                                                      ║\n╚═════════════════════════════════════════════════════════════════════════╝"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:50:00.535Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "018dd493fd601623e406-41d73aad2d0925c627d2", "file": "activity-cards-audit.spec.ts", "line": 145, "column": 3}, {"title": "should display activity cards with real database data", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 28311, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:12.018Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-0d60cf9ce736633ad7a3", "file": "activity-cards-audit.spec.ts", "line": 15, "column": 3}, {"title": "should test Join Activity button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 2, "status": "failed", "duration": 30030, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:55.385Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-96025218e73845ee402b", "file": "activity-cards-audit.spec.ts", "line": 32, "column": 3}, {"title": "should test Details button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 17, "parallelIndex": 2, "status": "failed", "duration": 26757, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:50:34.187Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-f7acd1033e86fd3e22e1", "file": "activity-cards-audit.spec.ts", "line": 62, "column": 3}, {"title": "should test Favorites/Heart button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 21, "parallelIndex": 2, "status": "failed", "duration": 26396, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:12.914Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-5e53d5351ae1620d6881", "file": "activity-cards-audit.spec.ts", "line": 92, "column": 3}, {"title": "should verify card layout and styling", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 26, "parallelIndex": 2, "status": "failed", "duration": 27284, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:43.066Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-a1d471825645a5c300b9", "file": "activity-cards-audit.spec.ts", "line": 116, "column": 3}, {"title": "should test responsive design", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 32, "parallelIndex": 2, "status": "interrupted", "duration": 10563, "error": {"message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:52:22.465Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-webkit\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "018dd493fd601623e406-28b5a68c48c50a92d4b8", "file": "activity-cards-audit.spec.ts", "line": 145, "column": 3}, {"title": "should display activity cards with real database data", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 25062, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:12.229Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-29dd465fa5be0781c647", "file": "activity-cards-audit.spec.ts", "line": 15, "column": 3}, {"title": "should test Join Activity button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 3, "status": "failed", "duration": 23751, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:57.178Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-2ba0f38301216c4887ac", "file": "activity-cards-audit.spec.ts", "line": 32, "column": 3}, {"title": "should test Details button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 19, "parallelIndex": 3, "status": "failed", "duration": 26678, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:50:35.646Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-a5594d50a741ad96e1d7", "file": "activity-cards-audit.spec.ts", "line": 62, "column": 3}, {"title": "should test Favorites/Heart button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "failed", "duration": 24599, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:13.418Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-0a01b4519eac1abd9585", "file": "activity-cards-audit.spec.ts", "line": 92, "column": 3}, {"title": "should verify card layout and styling", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 30, "parallelIndex": 3, "status": "failed", "duration": 24426, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:43.876Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Chrome\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-2f27bd5d74d202bb9991", "file": "activity-cards-audit.spec.ts", "line": 116, "column": 3}, {"title": "should test responsive design", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 35, "parallelIndex": 3, "status": "interrupted", "duration": 8387, "error": {"message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:52:23.329Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-Mobile-Chrome\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "018dd493fd601623e406-34c9ec6555d7d081f201", "file": "activity-cards-audit.spec.ts", "line": 145, "column": 3}, {"title": "should display activity cards with real database data", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 28555, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:12.300Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-652dec36086d19cfce69", "file": "activity-cards-audit.spec.ts", "line": 15, "column": 3}, {"title": "should test Join Activity button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 10, "parallelIndex": 4, "status": "failed", "duration": 29465, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:55.152Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Safari\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-998f2ba7c1a134eb4a27", "file": "activity-cards-audit.spec.ts", "line": 32, "column": 3}, {"title": "should test Details button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 1, "status": "failed", "duration": 26107, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:50:34.162Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-c5a2c533a5f66ada2615", "file": "activity-cards-audit.spec.ts", "line": 62, "column": 3}, {"title": "should test Favorites/Heart button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "failed", "duration": 26347, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:13.058Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-309c96eb0b890309c706", "file": "activity-cards-audit.spec.ts", "line": 92, "column": 3}, {"title": "should verify card layout and styling", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 27, "parallelIndex": 1, "status": "failed", "duration": 26978, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n=========================== logs ===========================\n  \"domcontentloaded\" event fired\n============================================================\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:43.165Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-21f30ebe18b75d5b394c", "file": "activity-cards-audit.spec.ts", "line": 116, "column": 3}, {"title": "should test responsive design", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 31, "parallelIndex": 1, "status": "interrupted", "duration": 10639, "error": {"message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:52:22.374Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-Mobile-Safari\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "018dd493fd601623e406-2a367713cf4a92f9de50", "file": "activity-cards-audit.spec.ts", "line": 145, "column": 3}, {"title": "should display activity cards with real database data", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 24865, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:12.343Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Tablet\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-628470d6212682bf2dd1", "file": "activity-cards-audit.spec.ts", "line": 15, "column": 3}, {"title": "should test Join Activity button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 12, "parallelIndex": 5, "status": "failed", "duration": 28973, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:49:55.314Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-88a58122e0fd8abef306", "file": "activity-cards-audit.spec.ts", "line": 32, "column": 3}, {"title": "should test Details button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 20, "parallelIndex": 4, "status": "failed", "duration": 28472, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:50:35.681Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-49e71-etails-button-functionality-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-be9d115d4c994049acc7", "file": "activity-cards-audit.spec.ts", "line": 62, "column": 3}, {"title": "should test Favorites/Heart button functionality", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 25, "parallelIndex": 4, "status": "failed", "duration": 23831, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.", "stack": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}, "message": "TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \n \u001b[90m 11 |\u001b[39m     \u001b[90m// Wait for activities to load (look for activity cards or loading state)\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"activity-card\"], .animate-spin, text=\"No activities found\"'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:13.407Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Tablet\\video.webm"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "018dd493fd601623e406-4a57ef44ac8bc3baf4ee", "file": "activity-cards-audit.spec.ts", "line": 92, "column": 3}, {"title": "should verify card layout and styling", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 29, "parallelIndex": 4, "status": "failed", "duration": 28551, "error": {"message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "TimeoutError: page.goto: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:51:43.518Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Tablet\\video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "018dd493fd601623e406-56552848b177ff38a926", "file": "activity-cards-audit.spec.ts", "line": 116, "column": 3}, {"title": "should test responsive design", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 34, "parallelIndex": 4, "status": "interrupted", "duration": 8620, "error": {"message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16", "location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Test ended.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5173/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the activities page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5173/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-08T22:52:22.794Z", "annotations": [], "attachments": [{"name": "_error-context-0", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\test-results\\artifacts\\activity-cards-audit-Activ-feb38-ould-test-responsive-design-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\CascadeProjects\\festival-family\\tests\\activity-cards-audit.spec.ts", "column": 16, "line": 6}}], "status": "skipped"}], "id": "018dd493fd601623e406-b84f72e0eee2889ee23f", "file": "activity-cards-audit.spec.ts", "line": 145, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-08T22:49:08.949Z", "duration": 208145.508, "expected": 0, "skipped": 5, "unexpected": 31, "flaky": 0}}