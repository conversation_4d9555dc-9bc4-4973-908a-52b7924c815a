// Removed Profile import - using Database types instead
import type { Database } from './supabase';

type Profile = Database['public']['Tables']['profiles']['Row'];

export enum AnnouncementDisplayType {
  BANNER = 'banner',
  CARD = 'card',
  TOAST = 'toast',
  POPUP = 'popup'
}

export type AnnouncementPriority = 'LOW' | 'MEDIUM' | 'HIGH'

export interface Announcement {
  id: string
  title: string
  content: string
  priority: AnnouncementPriority
  display_type?: AnnouncementDisplayType
  created_at: string
  updated_at: string
  created_by: string
  creator?: Profile
  is_active: boolean
  active?: boolean // For backward compatibility
  is_featured?: boolean
  target_audience?: string[]
  category?: {
    id: string
    name: string
    color: string | null
    description: string | null
    created_at: string
    updated_at: string
  } | null
  category_id?: string
  notification_sent?: boolean
  scheduled_for?: string | null
  expires_at?: string | null
  metadata?: Record<string, any>
}
