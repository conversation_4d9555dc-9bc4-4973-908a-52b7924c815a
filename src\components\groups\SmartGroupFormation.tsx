import React, { useState, useEffect } from 'react';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import {
  useGroupSuggestions,
  useCreateGroupSuggestion,
  useSuggestionResponses
} from '@/hooks/activity-coordination/useSmartGroupFormation';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Sparkles,
  Users,
  Music,
  Calendar,
  Target,
  Plus,
  Check,
  X,
  Clock,
  Zap
} from 'lucide-react';
import toast from '@/components/toast/Toast';
import { GroupSuggestionCard } from '@/components/activity-coordination/GroupSuggestionCard';

interface SmartGroupFormationProps {
  festivalId: string;
}

export const SmartGroupFormation: React.FC<SmartGroupFormationProps> = ({ 
  festivalId 
}) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'suggestions' | 'create'>('suggestions');
  const [formationType, setFormationType] = useState<'activity_based' | 'music_based' | 'hybrid'>('activity_based');
  
  // Form state
  const [formData, setFormData] = useState({
    suggestedName: '',
    suggestedDescription: '',
    activityFocus: [] as string[],
    musicFocus: [] as string[],
    minMembers: 3,
    maxMembers: 20
  });

  // Hooks
  const { suggestions, isLoading: suggestionsLoading } = useGroupSuggestions(user?.id, festivalId);
  const { 
    createActivityBasedSuggestion, 
    createMusicBasedSuggestion, 
    createHybridSuggestion,
    isCreating 
  } = useCreateGroupSuggestion();

  const handleCreateSuggestion = async () => {
    if (!user || !festivalId) {
      toast.error('Please log in to create group suggestions');
      return;
    }

    if (!formData.suggestedName.trim()) {
      toast.error('Please enter a group name');
      return;
    }

    try {
      const options = {
        suggestedName: formData.suggestedName,
        suggestedDescription: formData.suggestedDescription,
        minMembers: formData.minMembers,
        maxMembers: formData.maxMembers
      };

      switch (formationType) {
        case 'activity_based':
          if (formData.activityFocus.length === 0) {
            toast.error('Please add at least one activity focus');
            return;
          }
          await createActivityBasedSuggestion(
            festivalId,
            formData.activityFocus,
            options
          );
          break;

        case 'music_based':
          if (formData.musicFocus.length === 0) {
            toast.error('Please add at least one music focus');
            return;
          }
          await createMusicBasedSuggestion(
            festivalId,
            formData.musicFocus,
            options
          );
          break;

        case 'hybrid':
          if (formData.activityFocus.length === 0 || formData.musicFocus.length === 0) {
            toast.error('Please add both activity and music focus for hybrid suggestions');
            return;
          }
          await createHybridSuggestion(
            festivalId,
            formData.activityFocus,
            formData.musicFocus,
            options
          );
          break;
      }

      // Reset form and switch to suggestions tab
      setFormData({
        suggestedName: '',
        suggestedDescription: '',
        activityFocus: [],
        musicFocus: [],
        minMembers: 3,
        maxMembers: 20
      });
      setActiveTab('suggestions');
      // Note: refetch would be called here if available
      toast.success('Group suggestion created successfully!');
    } catch (error) {
      console.error('Error creating suggestion:', error);
      toast.error('Failed to create group suggestion');
    }
  };

  const addFocusItem = (type: 'activity' | 'music', item: string) => {
    if (!item.trim()) return;
    
    const focusKey = type === 'activity' ? 'activityFocus' : 'musicFocus';
    const currentItems = formData[focusKey];
    
    if (!currentItems.includes(item.trim())) {
      setFormData(prev => ({
        ...prev,
        [focusKey]: [...currentItems, item.trim()]
      }));
    }
  };

  const removeFocusItem = (type: 'activity' | 'music', item: string) => {
    const focusKey = type === 'activity' ? 'activityFocus' : 'musicFocus';
    setFormData(prev => ({
      ...prev,
      [focusKey]: prev[focusKey].filter(i => i !== item)
    }));
  };

  const getFormationTypeIcon = (type: string) => {
    switch (type) {
      case 'activity_based':
        return <Calendar className="w-4 h-4" />;
      case 'music_based':
        return <Music className="w-4 h-4" />;
      case 'hybrid':
        return <Zap className="w-4 h-4" />;
      default:
        return <Target className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Sparkles className="w-6 h-6 text-purple-600" />
          <h2 className="text-2xl font-bold text-gray-900">Smart Group Formation</h2>
        </div>
        <p className="text-gray-600">
          Find your festival tribe through intelligent group suggestions based on shared interests
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('suggestions')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'suggestions'
              ? 'bg-white text-purple-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <div className="flex items-center justify-center gap-2">
            <Users className="w-4 h-4" />
            Group Suggestions
          </div>
        </button>
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'create'
              ? 'bg-white text-purple-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <div className="flex items-center justify-center gap-2">
            <Plus className="w-4 h-4" />
            Create Suggestion
          </div>
        </button>
      </div>

      {/* Content */}
      {activeTab === 'suggestions' ? (
        <div className="space-y-4">
          {suggestionsLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="p-6 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </Card>
              ))}
            </div>
          ) : suggestions && suggestions.length > 0 ? (
            suggestions.map((suggestion) => (
              <GroupSuggestionCard
                key={suggestion.id}
                suggestion={suggestion}
                onRespond={(suggestionId, response, notes) => {
                  // Handle response logic here
                  console.log('Response:', suggestionId, response, notes);
                  // Note: refetch would be called here if available
                }}
              />
            ))
          ) : (
            <Card className="p-8 text-center">
              <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Group Suggestions Yet
              </h3>
              <p className="text-gray-500 mb-4">
                Create your first group suggestion to find festival buddies with shared interests!
              </p>
              <Button
                onClick={() => setActiveTab('create')}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Suggestion
              </Button>
            </Card>
          )}
        </div>
      ) : (
        <Card className="p-6">
          <div className="space-y-6">
            {/* Formation Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Group Formation Type
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {[
                  { type: 'activity_based', label: 'Activity Based', desc: 'Based on shared activities' },
                  { type: 'music_based', label: 'Music Based', desc: 'Based on music preferences' },
                  { type: 'hybrid', label: 'Hybrid', desc: 'Both activities and music' }
                ].map(({ type, label, desc }) => (
                  <button
                    key={type}
                    onClick={() => setFormationType(type as 'activity_based' | 'music_based' | 'hybrid')}
                    className={`p-4 rounded-lg border-2 text-left transition-colors ${
                      formationType === type
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {getFormationTypeIcon(type)}
                      <span className="font-medium">{label}</span>
                    </div>
                    <p className="text-sm text-gray-500">{desc}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Group Name *
                </label>
                <Input
                  value={formData.suggestedName}
                  onChange={(e) => setFormData(prev => ({ ...prev, suggestedName: e.target.value }))}
                  placeholder="e.g., Techno Lovers Unite"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <Textarea
                  value={formData.suggestedDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, suggestedDescription: e.target.value }))}
                  placeholder="Describe what this group is about..."
                  rows={3}
                />
              </div>
            </div>

            {/* Activity Focus (for activity_based and hybrid) */}
            {(formationType === 'activity_based' || formationType === 'hybrid') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Activity Focus *
                </label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add activity (e.g., Yoga, Food Tours, Photography)"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          addFocusItem('activity', e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={(e) => {
                        const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                        addFocusItem('activity', input.value);
                        input.value = '';
                      }}
                    >
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.activityFocus.map((activity) => (
                      <Badge
                        key={activity}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => removeFocusItem('activity', activity)}
                      >
                        {activity}
                        <X className="w-3 h-3 ml-1" />
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Music Focus (for music_based and hybrid) */}
            {(formationType === 'music_based' || formationType === 'hybrid') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Music Focus *
                </label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add music preference (e.g., Techno, Rock, Hip-Hop)"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          addFocusItem('music', e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={(e) => {
                        const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                        addFocusItem('music', input.value);
                        input.value = '';
                      }}
                    >
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.musicFocus.map((music) => (
                      <Badge
                        key={music}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => removeFocusItem('music', music)}
                      >
                        {music}
                        <X className="w-3 h-3 ml-1" />
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Group Size */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Min Members
                </label>
                <Input
                  type="number"
                  min="2"
                  max="50"
                  value={formData.minMembers}
                  onChange={(e) => setFormData(prev => ({ ...prev, minMembers: parseInt(e.target.value) || 3 }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Members
                </label>
                <Input
                  type="number"
                  min="3"
                  max="100"
                  value={formData.maxMembers}
                  onChange={(e) => setFormData(prev => ({ ...prev, maxMembers: parseInt(e.target.value) || 20 }))}
                />
              </div>
            </div>

            {/* Submit Button */}
            <Button
              onClick={handleCreateSuggestion}
              disabled={isCreating}
              className="w-full bg-purple-600 hover:bg-purple-700"
            >
              {isCreating ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Creating Suggestion...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Create Group Suggestion
                </>
              )}
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};

export default SmartGroupFormation;
