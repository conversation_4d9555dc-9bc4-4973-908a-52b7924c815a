import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useProfile } from '@/hooks/useProfile';
import { supabase } from '@/lib/supabase';
import { isAdminRole } from '@/lib/utils/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageUpload } from '@/components/ui/ImageUpload';
import { toast } from 'react-hot-toast';
import type { Database } from '@/types/supabase';

interface TipFormData {
  title: string;
  content: string;
  description?: string | null;
  category?: string | null;
  status?: string;
  is_featured?: boolean;
  tags?: string[];
  image_url?: string | null;
  estimated_read_time?: number;
  created_by?: string | null;
  created_at?: string;
  updated_at?: string | null;
}

const initialFormData: TipFormData = {
  title: '',
  content: '',
  description: '',
  category: 'SURVIVAL',
  status: 'draft',
  is_featured: false,
  tags: [],
  image_url: null,
  estimated_read_time: 2,
};

const TIP_CATEGORIES = [
  { value: 'SURVIVAL', label: 'Festival Survival' },
  { value: 'SOCIAL', label: 'Social & Networking' },
  { value: 'COMFORT', label: 'Comfort & Gear' },
  { value: 'BUDGET', label: 'Budget & Money' },
  { value: 'EXPERIENCE', label: 'Experience & Fun' },
  { value: 'SAFETY', label: 'Safety & Health' },
  { value: 'OTHER', label: 'Other' }
];

const STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', color: 'gray' },
  { value: 'published', label: 'Published', color: 'green' },
  { value: 'archived', label: 'Archived', color: 'red' }
];

const TipForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { profile, loading: profileLoading } = useProfile();
  const [formData, setFormData] = React.useState<TipFormData>(initialFormData);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
      return;
    }
  }, [profileLoading, profile, navigate]);

  React.useEffect(() => {
    if (id) {
      loadTip();
    }
  }, [id]);

  const loadTip = async () => {
    if (!id) return;

    try {
      const { data, error } = await supabase
        .from('tips')
        .select('*')
        .eq('id', id) // Fixed: Use UUID directly, not parseInt
        .single();

      if (error) throw error;
      if (data) {
        setFormData({
          ...data,
          tags: data.tags || [],
          estimated_read_time: 2, // Default value since field doesn't exist in tips table
          status: data.status || 'draft',
          is_featured: data.is_featured || false
        });
      }
    } catch (error) {
      console.error('Error loading tip:', error);
      setError('Failed to load tip');
      toast.error('Failed to load tip');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    const now = new Date().toISOString();
    const submitData = {
      ...formData,
      category: formData.category as Database["public"]["Enums"]["tip_category"] | null,
      updated_at: now,
      created_by: profile?.id,
      ...(id ? {} : { created_at: now }),
    };

    try {
      if (id) {
        const { error } = await supabase
          .from('tips')
          .update(submitData)
          .eq('id', id); // Fixed: Use UUID directly
        if (error) throw error;
        toast.success('Tip updated successfully!');
      } else {
        const { error } = await supabase
          .from('tips')
          .insert([submitData]);
        if (error) throw error;
        toast.success('Tip created successfully!');
      }

      navigate('/admin/tips');
    } catch (error) {
      console.error('Error saving tip:', error);
      setError('Failed to save tip');
      toast.error('Failed to save tip');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCategoryChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      category: value,
    }));
  };

  const handleStatusChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      status: value,
    }));
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setFormData((prev) => ({
      ...prev,
      tags,
    }));
  };

  const handleImageUpload = (url: string) => {
    setFormData((prev) => ({
      ...prev,
      image_url: url,
    }));
  };

  const handleImageRemove = () => {
    setFormData((prev) => ({
      ...prev,
      image_url: null,
    }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">{id ? 'Edit Tip' : 'Create New Tip'}</h1>
        {formData.status && (
          <Badge variant={formData.status === 'published' ? 'default' : 'secondary'}>
            {STATUS_OPTIONS.find(s => s.value === formData.status)?.label}
          </Badge>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="title" className="block text-sm font-medium mb-2">
                Title *
              </label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                placeholder="Enter tip title..."
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium mb-2">
                Description
              </label>
              <Textarea
                id="description"
                name="description"
                value={formData.description || ''}
                onChange={handleInputChange}
                rows={3}
                placeholder="Brief description of the tip..."
              />
            </div>

            <div>
              <label htmlFor="content" className="block text-sm font-medium mb-2">
                Content *
              </label>
              <Textarea
                id="content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                required
                rows={8}
                placeholder="Write your tip content here..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="category" className="block text-sm font-medium mb-2">
                  Category
                </label>
                <Select
                  value={formData.category || ''}
                  onValueChange={handleCategoryChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {TIP_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label htmlFor="estimated_read_time" className="block text-sm font-medium mb-2">
                  Read Time (minutes)
                </label>
                <Input
                  id="estimated_read_time"
                  name="estimated_read_time"
                  type="number"
                  min="1"
                  max="30"
                  value={formData.estimated_read_time || 2}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div>
              <label htmlFor="tags" className="block text-sm font-medium mb-2">
                Tags
              </label>
              <Input
                id="tags"
                name="tags"
                value={formData.tags?.join(', ') || ''}
                onChange={(e) => handleTagsChange(e.target.value)}
                placeholder="Enter tags separated by commas..."
              />
              <p className="text-sm text-gray-500 mt-1">
                Separate tags with commas (e.g., "camping, gear, essentials")
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Publishing & Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="status" className="block text-sm font-medium mb-2">
                  Status
                </label>
                <Select
                  value={formData.status || 'draft'}
                  onValueChange={handleStatusChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <Input
                  type="checkbox"
                  id="is_featured"
                  checked={formData.is_featured || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_featured: e.target.checked }))}
                  className="w-4 h-4"
                />
                <label htmlFor="is_featured" className="text-sm font-medium">
                  Featured Tip
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Image</CardTitle>
          </CardHeader>
          <CardContent>
            <ImageUpload
              onUpload={handleImageUpload}
              onRemove={handleImageRemove}
              currentImage={formData.image_url}
              bucket="activity-images"
              path="tips"
              placeholder="Upload tip image"
            />
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/admin/tips')}
            disabled={loading}
          >
            Cancel
          </Button>

          <div className="flex space-x-2">
            {formData.status !== 'draft' && (
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setFormData(prev => ({ ...prev, status: 'draft' }));
                  handleSubmit(new Event('submit') as any);
                }}
                disabled={loading}
              >
                Save as Draft
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : formData.status === 'published' ? 'Update Tip' : 'Save Tip'}
            </Button>
            {formData.status === 'draft' && (
              <Button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, status: 'published' }));
                  handleSubmit(new Event('submit') as any);
                }}
                disabled={loading}
              >
                Publish
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default TipForm;
