# Test info

- Name: Activity Cards Interactive Functionality Audit >> should test responsive design
- Location: C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:145:3

# Error details

```
Error: page.waitForSelector: Unexpected token "=" while parsing css selector "[data-testid="activity-card"], .animate-spin, text="No activities found"". Did you mean to CSS.escape it?
Call log:
  - waiting for [data-testid="activity-card"], .animate-spin, text="No activities found" to be visible

    at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:12:16
```

# Page snapshot

```yaml
- link "Skip to main content":
  - /url: "#main-content"
- navigation:
  - link "Festival Family":
    - /url: /
  - link "Sign In":
    - /url: /auth
- alert:
  - img
  - heading "Welcome to Festival Family!" [level=5]
  - text: high Featured We're excited to have you join our community of festival enthusiasts. Explore tips, guides, and connect with fellow music lovers to make the most of your festival experiences.
  - button:
    - img
- main:
  - img
  - heading "Festival Activities" [level=3]
  - button:
    - img
  - button:
    - img
  - img
  - textbox "Search activities..."
  - tablist:
    - tab "Meetups" [selected]:
      - img
      - text: Meetups
    - tab "Daily":
      - img
      - text: Daily
    - tab "Compete":
      - img
      - text: Compete
    - tab "Later":
      - img
      - text: Later
  - img
  - heading "Festival Meetups" [level=3]
  - paragraph: Connect with other festival-goers at organized meetups
  - img
  - heading "This activity demonstrates the complete admin-to-user pipeline working in production. Created by admin, stored in database, visible to users." [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: This activity demonstrates the complete admin-to-user pipeline working in production. Created by admin, stored in database, visible to users.
  - img
  - text: Production Testing Zone
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "Discover amazing local cuisine and meet fellow food lovers at our curated food truck gathering." [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: Discover amazing local cuisine and meet fellow food lovers at our curated food truck gathering.
  - img
  - text: Food Court Central
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "This activity tests the complete admin-to-user pipeline." [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: This activity tests the complete admin-to-user pipeline.
  - img
  - text: Pipeline Test Location
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "Final gathering to say goodbye and exchange contact information. Last chance to hand over camping gear!" [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: Final gathering to say goodbye and exchange contact information. Last chance to hand over camping gear!
  - img
  - text: Sziget Festival Base Camp
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "Official opening of the Festival Family hangout space at base camp. Meet your festival family and get oriented!" [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: Official opening of the Festival Family hangout space at base camp. Meet your festival family and get oriented!
  - img
  - text: Sziget Festival Base Camp
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "Final celebration and toast to another amazing Sziget experience with your Festival Family." [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: Final celebration and toast to another amazing Sziget experience with your Festival Family.
  - img
  - text: Budapest, Hungary
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "Dinner in Budapest for those staying an extra day. Continue the festival spirit in the city!" [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: Dinner in Budapest for those staying an extra day. Continue the festival spirit in the city!
  - img
  - text: Budapest, Hungary
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "Special dinner for Festival Family members at a location near Sziget Festival. Great opportunity to bond with your festival tribe!" [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: Special dinner for Festival Family members at a location near Sziget Festival. Great opportunity to bond with your festival tribe!
  - img
  - text: Restaurant near Sziget Festival
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - img
  - heading "Daily gathering point for Festival Family members. Connect, share experiences, and take group photos." [level=4]
  - text: meetup
  - button:
    - img
  - button:
    - img
  - paragraph: Daily gathering point for Festival Family members. Connect, share experiences, and take group photos.
  - img
  - text: Sziget Festival Base Camp
  - img
  - text: Activity available meetup
  - button "Join Activity"
  - button "Details":
    - text: Details
    - img
  - button:
    - img
- navigation "Main navigation":
  - button "Sign In to Continue"
  - button "Navigate to Home": Home
  - button "Navigate to Activities": Activities
  - button "Navigate to FamHub": FamHub
  - button "Navigate to Discover": Discover
  - button "Open quick actions menu": Actions
- heading "Connected" [level=3]
- paragraph: Successfully connected to Supabase
- button "Details"
- button "Dismiss"
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Activity Cards Interactive Functionality Audit', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Navigate to the activities page
   6 |     await page.goto('http://localhost:5173/activities');
   7 |     
   8 |     // Wait for the page to load
   9 |     await page.waitForLoadState('networkidle');
   10 |     
   11 |     // Wait for activities to load (look for activity cards or loading state)
>  12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
      |                ^ Error: page.waitForSelector: Unexpected token "=" while parsing css selector "[data-testid="activity-card"], .animate-spin, text="No activities found"". Did you mean to CSS.escape it?
   13 |   });
   14 |
   15 |   test('should display activity cards with real database data', async ({ page }) => {
   16 |     // Take a screenshot of the activities page
   17 |     await page.screenshot({ path: 'test-results/activities-page-initial.png', fullPage: true });
   18 |     
   19 |     // Check if activities are loaded
   20 |     const activityCards = await page.locator('[data-testid="activity-card"]').count();
   21 |     const loadingSpinner = await page.locator('.animate-spin').count();
   22 |     const noActivitiesMessage = await page.locator('text="No activities found"').count();
   23 |     
   24 |     console.log(`Found ${activityCards} activity cards`);
   25 |     console.log(`Loading spinners: ${loadingSpinner}`);
   26 |     console.log(`No activities messages: ${noActivitiesMessage}`);
   27 |     
   28 |     // Verify that either activities are loaded or we have a proper empty state
   29 |     expect(activityCards > 0 || noActivitiesMessage > 0).toBeTruthy();
   30 |   });
   31 |
   32 |   test('should test Join Activity button functionality', async ({ page }) => {
   33 |     // Look for Join Activity buttons
   34 |     const joinButtons = page.locator('button:has-text("Join Activity"), button:has-text("Join")');
   35 |     const joinButtonCount = await joinButtons.count();
   36 |
   37 |     console.log(`Found ${joinButtonCount} Join Activity buttons`);
   38 |
   39 |     if (joinButtonCount > 0) {
   40 |       // Click the first Join Activity button
   41 |       await joinButtons.first().click();
   42 |
   43 |       // Wait for any response (success message, error, or state change)
   44 |       await page.waitForTimeout(3000);
   45 |
   46 |       // Take screenshot after clicking
   47 |       await page.screenshot({ path: 'test-results/after-join-click.png', fullPage: true });
   48 |
   49 |       // Check for success/error messages or button state changes
   50 |       const successMessage = await page.locator('text="Joined activity", text="Successfully joined", text="Please log in"').count();
   51 |       const errorMessage = await page.locator('text="Failed", text="Error"').count();
   52 |       const buttonStateChanged = await page.locator('button:has-text("Leave"), button:has-text("Joined")').count();
   53 |       const loadingSpinner = await page.locator('.animate-spin').count();
   54 |
   55 |       console.log(`Success/login messages: ${successMessage}`);
   56 |       console.log(`Error messages: ${errorMessage}`);
   57 |       console.log(`Button state changed: ${buttonStateChanged}`);
   58 |       console.log(`Loading spinners: ${loadingSpinner}`);
   59 |     }
   60 |   });
   61 |
   62 |   test('should test Details button functionality', async ({ page }) => {
   63 |     // Look for Details buttons
   64 |     const detailsButtons = page.locator('button:has-text("Details"), button:has-text("View Details")');
   65 |     const detailsButtonCount = await detailsButtons.count();
   66 |
   67 |     console.log(`Found ${detailsButtonCount} Details buttons`);
   68 |
   69 |     if (detailsButtonCount > 0) {
   70 |       // Click the first Details button
   71 |       await detailsButtons.first().click();
   72 |
   73 |       // Wait for modal or navigation
   74 |       await page.waitForTimeout(2000);
   75 |
   76 |       // Take screenshot after clicking
   77 |       await page.screenshot({ path: 'test-results/after-details-click.png', fullPage: true });
   78 |
   79 |       // Check for modal, new page, or expanded content
   80 |       const modal = await page.locator('[role="dialog"], .modal, [data-testid="activity-modal"]').count();
   81 |       const expandedContent = await page.locator('[data-testid="activity-details"]').count();
   82 |       const modalBackdrop = await page.locator('.fixed.inset-0.bg-black\\/50').count();
   83 |       const modalContent = await page.locator('text="About This Activity", text="Details", text="Close"').count();
   84 |
   85 |       console.log(`Modals opened: ${modal}`);
   86 |       console.log(`Expanded content: ${expandedContent}`);
   87 |       console.log(`Modal backdrop: ${modalBackdrop}`);
   88 |       console.log(`Modal content elements: ${modalContent}`);
   89 |     }
   90 |   });
   91 |
   92 |   test('should test Favorites/Heart button functionality', async ({ page }) => {
   93 |     // Look for heart/favorite buttons
   94 |     const favoriteButtons = page.locator('button:has([data-lucide="heart"]), button[aria-label*="favorite"], button[aria-label*="heart"]');
   95 |     const favoriteButtonCount = await favoriteButtons.count();
   96 |     
   97 |     console.log(`Found ${favoriteButtonCount} Favorite buttons`);
   98 |     
   99 |     if (favoriteButtonCount > 0) {
  100 |       // Click the first favorite button
  101 |       await favoriteButtons.first().click();
  102 |       
  103 |       // Wait for state change
  104 |       await page.waitForTimeout(2000);
  105 |       
  106 |       // Take screenshot after clicking
  107 |       await page.screenshot({ path: 'test-results/after-favorite-click.png', fullPage: true });
  108 |       
  109 |       // Check for visual state changes (filled heart, color change, etc.)
  110 |       const filledHearts = await page.locator('[data-lucide="heart"].fill-current, .text-red-400, .text-red-500').count();
  111 |       
  112 |       console.log(`Filled hearts after click: ${filledHearts}`);
```