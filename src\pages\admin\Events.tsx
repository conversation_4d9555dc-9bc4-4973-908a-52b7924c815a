import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { useProfile } from '@/hooks/useProfile';
import { isAdminRole } from '@/lib/utils/auth';
import type { Database } from '@/types/supabase'; // Import from single source of truth
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trash } from 'lucide-react';
import { format } from 'date-fns';
import { UserRole } from '@/types/core';

type Event = Database['public']['Tables']['events']['Row'];

const statusColors: Record<string, string> = {
  DRAFT: 'bg-gray-100 text-gray-800',
  PUBLISHED: 'bg-green-100 text-green-800',
  ARCHIVED: 'bg-red-100 text-red-800',
};

const Events = () => {
  const navigate = useNavigate();
  const { profile, isLoading: profileLoading } = useProfile(); // Correct property name
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [profileLoading, profile, navigate]);

  useEffect(() => {
    if (!profileLoading && isAdminRole(profile?.role)) {
      loadEvents();
    }
  }, [profileLoading, profile]);

  const loadEvents = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .order('start_date', { ascending: false });

      if (error) throw error;
      setEvents(data || []);
    } catch (error) {
      console.error('Error loading events:', error);
      setError('Failed to load events');
    } finally {
      setLoading(false);
    }
  };



  const toggleEventStatus = async (event: Event) => {
    try {
      const newStatus = event.status === 'PUBLISHED' ? 'ARCHIVED' : 'PUBLISHED';
      const { error } = await supabase
        .from('events')
        .update({
          status: newStatus,
          is_active: newStatus === 'PUBLISHED',
          updated_at: new Date().toISOString()
        })
        .eq('id', event.id);

      if (error) throw error;
      await loadEvents();
    } catch (error) {
      console.error('Error updating event status:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('events')
        .delete()
        .eq('id', id);

      if (error) throw error;
      await loadEvents();
    } catch (error) {
      console.error('Error deleting event:', error);
      setError('Failed to delete event');
    }
  };

  if (profileLoading) {
    return <div className="p-8">Loading...</div>;
  }

  if (!isAdminRole(profile?.role)) {
    return <div className="p-8 text-center">Access Denied</div>;
  }

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-purple-900">Events</h1>
        <Button onClick={() => navigate('/admin/events/new')}>
          Create Event
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {events.map((event) => (
              <tr key={event.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {event.title}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {format(new Date(event.start_date), 'PPP')} - {format(new Date(event.end_date), 'PPP')}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <Badge
                    variant="secondary"
                    className={statusColors[event.status || 'DRAFT']}
                  >
                    {event.status || 'DRAFT'}
                  </Badge>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => toggleEventStatus(event)}
                    className={`mr-4 px-2 py-1 rounded ${
                      event.is_active ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {event.is_active ? 'Active' : 'Inactive'}
                  </button>
                  <Button
                    onClick={() => navigate(`/admin/events/${event.id}/edit`)}
                    className="mr-2"
                  >
                    Edit
                  </Button>
                  <Button 
                    onClick={() => handleDelete(event.id)}
                    variant="destructive"
                    size="sm"
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Events;
