import React from 'react';
import { type Event } from '@/types';

interface EventCardProps {
  event: Event;
}

const EventCard: React.FC<EventCardProps> = ({ event }) => {
  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
      <h3 className="text-lg font-semibold">{event.title}</h3>
      <p className="text-sm text-white/70 mt-2">{event.description}</p>
      <div className="mt-4 flex justify-between items-center">
        <span className="text-sm text-white/60">{event.location}</span>
        <span className="text-sm text-white/60">{new Date(event.start_date).toLocaleDateString()}</span>
      </div>
    </div>
  );
};

export default EventCard; 