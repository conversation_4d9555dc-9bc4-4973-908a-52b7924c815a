import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '@/hooks/useProfile';
import { supabase } from '@/lib/supabase';
import { isAdminRole } from '@/lib/utils/auth';
import { type Faq } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { MoreVertical, Pencil, Trash, HelpCircle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

const FAQs: React.FC = () => {
  const navigate = useNavigate();
  const { profile, loading: profileLoading } = useProfile();
  const [faqs, setFaqs] = React.useState<Faq[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    loadFAQs();
  }, []);

  React.useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [profileLoading, profile, navigate]);

  const loadFAQs = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('faqs')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setFaqs(data || []);
    } catch (error) {
      console.error('Error loading FAQs:', error);
      setError('Failed to load FAQs');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this FAQ?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('faqs')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setFaqs(faqs.filter(f => f.id !== id));
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      setError('Failed to delete FAQ');
    }
  };

  if (profileLoading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">FAQs</h1>
          <p className="text-muted-foreground mt-2">
            Manage frequently asked questions
          </p>
        </div>
        <Button
          onClick={() => navigate('/admin/faqs/new')}
          className="bg-primary/20 hover:bg-primary/30"
        >
          Create FAQ
        </Button>
      </div>

      {error && (
        <div className="rounded-lg border border-red-500/20 p-4 text-red-400 bg-red-500/10">
          {error}
        </div>
      )}

      {loading ? (
        <div className="text-center py-8 text-muted-foreground">
          Loading FAQs...
        </div>
      ) : faqs.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No FAQs found</CardTitle>
            <CardDescription>
              Get started by creating your first FAQ
            </CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <div className="grid gap-6">
          {faqs.map((faq) => (
            <Card key={faq.id}>
              <CardHeader className="flex flex-row items-start justify-between space-y-0">
                <div className="space-y-1">
                  <CardTitle className="flex items-center gap-2">
                    <HelpCircle className="h-5 w-5" />
                    {faq.question}
                  </CardTitle>
                  {faq.answer && (
                    <CardDescription>{faq.answer}</CardDescription>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => navigate(`/admin/faqs/${faq.id}`)}
                    >
                      <Pencil className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-red-400"
                      onClick={() => handleDelete(faq.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Badge
                      variant="secondary"
                      className={cn(
                        'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30'
                      )}
                    >
                      FAQ
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      Last updated {faq.updated_at ? format(new Date(faq.updated_at), 'PPP') : 'Never'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default FAQs;
