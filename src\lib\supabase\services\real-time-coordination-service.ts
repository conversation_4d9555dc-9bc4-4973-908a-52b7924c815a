/**
 * Real-Time Coordination Service
 * 
 * Handles real-time activity coordination, live updates, and instant connections.
 * Enables spontaneous meetups and live festival coordination.
 * 
 * @module RealTimeCoordinationService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from './base-service'
import type { RealtimeChannel } from '@supabase/supabase-js'
import type { 
  ActivityCoordinationSuggestion,
  LiveActivityStatus,
  ActivityCoordinationSubscription,
  ActivityCoordinationEvent,
  CoordinationStatus
} from '../activity-coordination/types'
import type { Profile, Activity } from '../database.types'

/**
 * Service for real-time activity coordination and live updates
 */
export class RealTimeCoordinationService extends BaseService {
  private subscriptions: Map<string, RealtimeChannel> = new Map()
  private eventHandlers: Map<string, (event: ActivityCoordinationEvent) => void> = new Map()

  /**
   * Subscribe to real-time updates for an activity
   */
  async subscribeToActivity(
    activityId: string,
    onUpdate: (event: ActivityCoordinationEvent) => void
  ): Promise<ServiceResponse<string>> {
    return this.handleResponse(
      (async () => {
        const channelName = `activity_coordination:${activityId}`
        
        // Remove existing subscription if any
        if (this.subscriptions.has(channelName)) {
          await this.unsubscribeFromActivity(activityId)
        }

        const channel = this.client
          .channel(channelName)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'activity_attendance',
              filter: `activity_id=eq.${activityId}`
            },
            (payload) => {
              const event: ActivityCoordinationEvent = {
                type: 'ATTENDANCE_SET',
                payload: payload.new as any
              }
              onUpdate(event)
            }
          )
          .subscribe()

        this.subscriptions.set(channelName, channel)
        this.eventHandlers.set(channelName, onUpdate)

        return channelName
      })()
    )
  }

  /**
   * Unsubscribe from activity updates
   */
  async unsubscribeFromActivity(activityId: string): Promise<ServiceResponse<boolean>> {
    return this.handleResponse(
      (async () => {
        const channelName = `activity_coordination:${activityId}`
        const channel = this.subscriptions.get(channelName)
        
        if (channel) {
          await this.client.removeChannel(channel)
          this.subscriptions.delete(channelName)
          this.eventHandlers.delete(channelName)
        }

        return true
      })()
    )
  }

  /**
   * Subscribe to festival-wide coordination updates
   */
  async subscribeToFestival(
    festivalId: string,
    onUpdate: (event: ActivityCoordinationEvent) => void
  ): Promise<ServiceResponse<string>> {
    return this.handleResponse(
      (async () => {
        const channelName = `festival_coordination:${festivalId}`
        
        const channel = this.client
          .channel(channelName)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'activity_attendance'
            },
            (payload) => {
              // Filter for activities in this festival
              this.handleFestivalUpdate(festivalId, payload, onUpdate)
            }
          )
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'group_external_links'
            },
            (payload) => {
              const event: ActivityCoordinationEvent = {
                type: 'EXTERNAL_LINK_ADDED',
                payload: payload.new as any
              }
              onUpdate(event)
            }
          )
          .subscribe()

        this.subscriptions.set(channelName, channel)
        this.eventHandlers.set(channelName, onUpdate)

        return channelName
      })()
    )
  }

  /**
   * Get live coordination suggestions for a user
   */
  async getLiveCoordinationSuggestions(
    userId: string,
    festivalId: string,
    location?: { lat: number; lng: number }
  ): Promise<ServiceResponse<ActivityCoordinationSuggestion[]>> {
    return this.handleResponse(
      (async () => {
        // Get user's current activity attendance
        const { data: userAttendance, error: attendanceError } = await this.client
          .from('activity_attendance')
          .select(`
            *,
            activities!activity_attendance_activity_id_fkey(*)
          `)
          .eq('user_id', userId)
          .eq('status', 'going')

        if (attendanceError) throw attendanceError

        // Get activities happening soon in the festival
        const now = new Date()
        const soonThreshold = new Date(now.getTime() + 2 * 60 * 60 * 1000) // 2 hours from now

        const { data: upcomingActivities, error: activitiesError } = await this.client
          .from('activities')
          .select('*')
          .eq('festival_id', festivalId)
          .gte('start_date', now.toISOString())
          .lte('start_date', soonThreshold.toISOString())

        if (activitiesError) throw activitiesError

        const suggestions: ActivityCoordinationSuggestion[] = []

        // Create suggestions for upcoming activities
        for (const activity of upcomingActivities || []) {
          // Get others attending this activity
          const { data: attendees, error: attendeesError } = await this.client
            .from('activity_attendance')
            .select(`
              profiles!activity_attendance_user_id_fkey(
                id, username, full_name, avatar_url
              )
            `)
            .eq('activity_id', activity.id)
            .eq('status', 'going')
            .neq('user_id', userId)

          if (attendeesError) continue

          const attendeeProfiles = attendees?.map(a => a.profiles).filter(Boolean) || []

          if (attendeeProfiles.length > 0) {
            suggestions.push({
              id: `live_${activity.id}`,
              type: 'activity_buddy',
              title: `Join ${activity.title}`,
              description: `${attendeeProfiles.length} others are going to this activity`,
              activity: activity as any,
              suggested_users: (attendeeProfiles as any[]).map((profile: any) => ({
                ...profile,
                created_at: profile.created_at || new Date().toISOString(),
                role: profile.role || 'user',
                updated_at: profile.updated_at || new Date().toISOString(),
                bio: profile.bio || null
              })) as Profile[],
              reason: 'Activity starting soon',
              confidence_score: 0.9,
              expires_at: activity.start_date
            })
          }
        }

        // Add spontaneous meetup suggestions
        if (location) {
          suggestions.push({
            id: `spontaneous_${Date.now()}`,
            type: 'spontaneous_meetup',
            title: 'Start a spontaneous meetup',
            description: 'Create an impromptu gathering for others nearby',
            suggested_users: [],
            reason: 'Based on your location',
            confidence_score: 0.7,
            location: `${location.lat},${location.lng}`
          })
        }

        return suggestions.slice(0, 10) // Limit to top 10 suggestions
      })()
    )
  }

  /**
   * Create a spontaneous activity coordination
   */
  async createSpontaneousCoordination(
    userId: string,
    title: string,
    description: string,
    location?: string,
    activityType?: string
  ): Promise<ServiceResponse<ActivityCoordinationSuggestion>> {
    return this.handleResponse(
      (async () => {
        const coordination: ActivityCoordinationSuggestion = {
          id: `spontaneous_${Date.now()}_${userId}`,
          type: 'spontaneous_meetup',
          title,
          description,
          suggested_users: [],
          reason: 'User-created spontaneous meetup',
          confidence_score: 1.0,
          expires_at: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // Expires in 2 hours
          location
        }

        // In a real implementation, this would be stored in a coordination table
        // and broadcast to nearby users or those with matching interests

        return coordination
      })()
    )
  }

  /**
   * Join a coordination suggestion
   */
  async joinCoordination(
    userId: string,
    coordinationId: string
  ): Promise<ServiceResponse<boolean>> {
    return this.handleResponse(
      (async () => {
        // In a real implementation, this would:
        // 1. Add user to the coordination
        // 2. Notify other participants
        // 3. Update the coordination status

        // For now, we'll simulate success
        return true
      })()
    )
  }

  /**
   * Get active coordinations for a festival
   */
  async getActiveCoordinations(
    festivalId: string,
    userId?: string
  ): Promise<ServiceResponse<ActivityCoordinationSuggestion[]>> {
    return this.handleResponse(
      (async () => {
        // This would query a coordinations table in a real implementation
        // For now, return empty array as placeholder
        return []
      })()
    )
  }

  /**
   * Broadcast a coordination update
   */
  async broadcastCoordinationUpdate(
    channelName: string,
    event: ActivityCoordinationEvent
  ): Promise<ServiceResponse<boolean>> {
    return this.handleResponse(
      (async () => {
        const channel = this.subscriptions.get(channelName)
        
        if (channel) {
          // Send custom event through the channel
          await channel.send({
            type: 'broadcast',
            event: 'coordination_update',
            payload: event
          })
        }

        return true
      })()
    )
  }

  /**
   * Clean up all subscriptions
   */
  async cleanup(): Promise<void> {
    // Convert Map to array for iteration compatibility
    const subscriptionEntries = Array.from(this.subscriptions.entries())
    for (const [channelName, channel] of subscriptionEntries) {
      await this.client.removeChannel(channel)
    }
    this.subscriptions.clear()
    this.eventHandlers.clear()
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async handleFestivalUpdate(
    festivalId: string,
    payload: any,
    onUpdate: (event: ActivityCoordinationEvent) => void
  ): Promise<void> {
    try {
      // Check if the activity belongs to this festival
      const { data: activity, error } = await this.client
        .from('activities')
        .select('festival_id')
        .eq('id', payload.new?.activity_id)
        .single()

      if (!error && activity?.festival_id === festivalId) {
        const event: ActivityCoordinationEvent = {
          type: 'ATTENDANCE_SET',
          payload: payload.new
        }
        onUpdate(event)
      }
    } catch (error) {
      console.error('Error handling festival update:', error)
    }
  }
}

// Export singleton instance
export const realTimeCoordinationService = new RealTimeCoordinationService()
