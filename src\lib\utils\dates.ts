// Removed invalid import - using Database types instead
import { Database } from '@/types/supabase';

type Event = Database['public']['Tables']['events']['Row'];

/**
 * Validates if an event object meets the minimum required properties
 * @param event The event object to validate
 * @returns Boolean indicating if the event is valid
 */
export function isValidEvent(event: any): event is Event {
  return (
    event &&
    typeof event === 'object' &&
    typeof event.id === 'string' &&
    typeof event.title === 'string' &&
    typeof event.start_date === 'string' &&
    typeof event.status === 'string'
  );
}

/**
 * Formats event dates into a human-readable string
 * @param startDate ISO 8601 formatted start date
 * @param endDate Optional ISO 8601 formatted end date
 * @returns Formatted date string
 */
export function formatEventDate(startDate: string, endDate?: string): string {
  if (!startDate) return 'Date not available';

  const start = new Date(startDate);
  if (isNaN(start.getTime())) return 'Invalid date';

  if (!endDate || startDate === endDate) {
    return start.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  const end = new Date(endDate);
  if (isNaN(end.getTime())) return 'Invalid date range';
  
  // If dates are in the same month and year
  if (start.getMonth() === end.getMonth() && start.getFullYear() === end.getFullYear()) {
    return `${start.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })} - 
            ${end.toLocaleDateString('en-US', { day: 'numeric', year: 'numeric' })}`;
  }

  // Different months or years
  return `${start.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })} - 
          ${end.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}`;
}

/**
 * Checks if an event is currently active
 * @param event Event to check
 * @returns Boolean indicating if the event is currently happening
 */
export function isEventActive(event: Event): boolean {
  const now = new Date();
  const startDate = new Date(event.start_date);
  const endDate = new Date(event.end_date);

  return now >= startDate && now <= endDate;
}