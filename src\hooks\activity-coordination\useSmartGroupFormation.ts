/**
 * Smart Group Formation Hooks
 * 
 * React hooks for intelligent group suggestions and formation based on activity
 * attendance and music preferences. Enables community-first group building.
 * 
 * @module useSmartGroupFormation
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { smartGroupFormationService, groupService } from '@/lib/supabase/services'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import type { 
  GroupSuggestion,
  GroupSuggestionResponse,
  GroupFormationType,
  SuggestionStatus,
  SmartGroupFormationRequest,
  GroupFormationInsights,
  SmartGroup
} from '@/lib/supabase/activity-coordination/types'
import type { Group } from '@/types'

/**
 * Hook for managing group suggestions for a user
 */
export function useGroupSuggestions(userId?: string, festivalId?: string, status: SuggestionStatus = 'pending') {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const { data: suggestions, isLoading, error } = useQuery({
    queryKey: ['group-suggestions', user?.id, festivalId, status],
    queryFn: () => user ? smartGroupFormationService.getUserGroupSuggestions(user.id, festivalId, status) : null,
    enabled: !!user,
    select: (response) => response?.data || []
  })

  const respondToSuggestionMutation = useMutation({
    mutationFn: ({ suggestionId, response, notes }: { 
      suggestionId: string
      response: boolean
      notes?: string 
    }) => {
      if (!user) throw new Error('User not authenticated')
      return smartGroupFormationService.respondToGroupSuggestion(suggestionId, user.id, response, notes)
    },
    onSuccess: () => {
      // Invalidate suggestions queries
      queryClient.invalidateQueries({ queryKey: ['group-suggestions'] })
      queryClient.invalidateQueries({ queryKey: ['user-groups'] })
    }
  })

  const respondToSuggestion = useCallback((
    suggestionId: string, 
    response: boolean, 
    notes?: string
  ) => {
    respondToSuggestionMutation.mutate({ suggestionId, response, notes })
  }, [respondToSuggestionMutation])

  return {
    suggestions,
    isLoading,
    error,
    respondToSuggestion,
    isResponding: respondToSuggestionMutation.isPending,
    responseError: respondToSuggestionMutation.error
  }
}

/**
 * Hook for creating smart group suggestions
 */
export function useCreateGroupSuggestion() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const createActivityBasedSuggestionMutation = useMutation({
    mutationFn: ({
      festivalId,
      activityFocus,
      options = {}
    }: {
      festivalId: string
      activityFocus: string[]
      options?: {
        suggestedName?: string
        suggestedDescription?: string
        minMembers?: number
        maxMembers?: number
        confidenceThreshold?: number
      }
    }) => {
      if (!user) throw new Error('User not authenticated')
      return smartGroupFormationService.createActivityBasedGroupSuggestion(
        user.id,
        festivalId,
        activityFocus,
        options
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group-suggestions'] })
    }
  })

  const createMusicBasedSuggestionMutation = useMutation({
    mutationFn: ({
      festivalId,
      musicFocus,
      options = {}
    }: {
      festivalId: string
      musicFocus: string[]
      options?: {
        suggestedName?: string
        suggestedDescription?: string
        minMembers?: number
        maxMembers?: number
        focusType?: 'artists' | 'genres'
      }
    }) => {
      if (!user) throw new Error('User not authenticated')
      return smartGroupFormationService.createMusicBasedGroupSuggestion(
        user.id,
        festivalId,
        musicFocus,
        options
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group-suggestions'] })
    }
  })

  const createHybridSuggestionMutation = useMutation({
    mutationFn: ({
      festivalId,
      activityFocus,
      musicFocus,
      options = {}
    }: {
      festivalId: string
      activityFocus: string[]
      musicFocus: string[]
      options?: {
        suggestedName?: string
        suggestedDescription?: string
        minMembers?: number
        maxMembers?: number
      }
    }) => {
      if (!user) throw new Error('User not authenticated')
      return smartGroupFormationService.createHybridGroupSuggestion(
        user.id,
        festivalId,
        activityFocus,
        musicFocus,
        options
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['group-suggestions'] })
    }
  })

  const createActivityBasedSuggestion = useCallback((
    festivalId: string,
    activityFocus: string[],
    options?: {
      suggestedName?: string
      suggestedDescription?: string
      minMembers?: number
      maxMembers?: number
      confidenceThreshold?: number
    }
  ) => {
    createActivityBasedSuggestionMutation.mutate({ festivalId, activityFocus, options })
  }, [createActivityBasedSuggestionMutation])

  const createMusicBasedSuggestion = useCallback((
    festivalId: string,
    musicFocus: string[],
    options?: {
      suggestedName?: string
      suggestedDescription?: string
      minMembers?: number
      maxMembers?: number
      focusType?: 'artists' | 'genres'
    }
  ) => {
    createMusicBasedSuggestionMutation.mutate({ festivalId, musicFocus, options })
  }, [createMusicBasedSuggestionMutation])

  const createHybridSuggestion = useCallback((
    festivalId: string,
    activityFocus: string[],
    musicFocus: string[],
    options?: {
      suggestedName?: string
      suggestedDescription?: string
      minMembers?: number
      maxMembers?: number
    }
  ) => {
    createHybridSuggestionMutation.mutate({ festivalId, activityFocus, musicFocus, options })
  }, [createHybridSuggestionMutation])

  return {
    createActivityBasedSuggestion,
    createMusicBasedSuggestion,
    createHybridSuggestion,
    isCreating: createActivityBasedSuggestionMutation.isPending ||
                createMusicBasedSuggestionMutation.isPending ||
                createHybridSuggestionMutation.isPending,
    isCreatingActivityBased: createActivityBasedSuggestionMutation.isPending,
    isCreatingMusicBased: createMusicBasedSuggestionMutation.isPending,
    isCreatingHybrid: createHybridSuggestionMutation.isPending,
    error: createActivityBasedSuggestionMutation.error ||
           createMusicBasedSuggestionMutation.error ||
           createHybridSuggestionMutation.error
  }
}

/**
 * Hook for creating spontaneous groups
 */
export function useCreateSpontaneousGroup() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const createSpontaneousGroupMutation = useMutation({
    mutationFn: ({
      name,
      description,
      festivalId,
      options = {}
    }: {
      name: string
      description: string
      festivalId: string
      options?: {
        activityFocus?: string[]
        musicFocus?: string[]
        maxMembers?: number
        expiresInHours?: number
      }
    }) => {
      if (!user) throw new Error('User not authenticated')
      return smartGroupFormationService.createSpontaneousGroup(
        user.id,
        name,
        description,
        festivalId,
        options
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-groups'] })
      queryClient.invalidateQueries({ queryKey: ['festival-groups'] })
    }
  })

  const createSpontaneousGroup = useCallback((
    name: string,
    description: string,
    festivalId: string,
    options?: {
      activityFocus?: string[]
      musicFocus?: string[]
      maxMembers?: number
      expiresInHours?: number
    }
  ) => {
    createSpontaneousGroupMutation.mutate({ name, description, festivalId, options })
  }, [createSpontaneousGroupMutation])

  return {
    createSpontaneousGroup,
    isCreating: createSpontaneousGroupMutation.isPending,
    error: createSpontaneousGroupMutation.error,
    createdGroup: createSpontaneousGroupMutation.data?.data
  }
}

/**
 * Hook for getting group formation insights
 */
export function useGroupFormationInsights(festivalId: string) {
  const { user } = useAuth()

  const { data: insights, isLoading, error } = useQuery({
    queryKey: ['group-formation-insights', user?.id, festivalId],
    queryFn: () => user ? smartGroupFormationService.getGroupFormationInsights(user.id, festivalId) : null,
    enabled: !!user && !!festivalId,
    select: (response) => response?.data
  })

  return {
    insights,
    isLoading,
    error
  }
}

/**
 * Hook for getting smart group recommendations
 */
export function useSmartGroupRecommendations(festivalId: string) {
  const { user } = useAuth()

  const { data: activityBasedGroups, isLoading: isLoadingActivity } = useQuery({
    queryKey: ['activity-based-group-recommendations', user?.id, festivalId],
    queryFn: () => user ? groupService.getActivityBasedGroupRecommendations(user.id, festivalId) : null,
    enabled: !!user && !!festivalId,
    select: (response) => response?.data || []
  })

  const { data: musicBasedGroups, isLoading: isLoadingMusic } = useQuery({
    queryKey: ['music-based-group-recommendations', user?.id, festivalId],
    queryFn: () => user ? groupService.getMusicBasedGroupRecommendations(user.id, festivalId) : null,
    enabled: !!user && !!festivalId,
    select: (response) => response?.data || []
  })

  const { data: spontaneousGroups, isLoading: isLoadingSpontaneous } = useQuery({
    queryKey: ['spontaneous-groups', festivalId],
    queryFn: () => groupService.getActiveSpontaneousGroups(festivalId),
    enabled: !!festivalId,
    select: (response) => response?.data || [],
    refetchInterval: 60000 // Refetch every minute for live updates
  })

  return {
    activityBasedGroups,
    musicBasedGroups,
    spontaneousGroups,
    isLoading: isLoadingActivity || isLoadingMusic || isLoadingSpontaneous,
    isLoadingActivity,
    isLoadingMusic,
    isLoadingSpontaneous
  }
}

/**
 * Hook for getting suggestion responses
 */
export function useSuggestionResponses(suggestionId: string) {
  const { data: responses, isLoading, error } = useQuery({
    queryKey: ['suggestion-responses', suggestionId],
    queryFn: () => smartGroupFormationService.getSuggestionResponses(suggestionId),
    enabled: !!suggestionId,
    select: (response) => response?.data || []
  })

  return {
    responses,
    isLoading,
    error
  }
}
