import type { Profile, Activity, Festival } from '@/types'; // Import from unified types
import { FestivalType } from '@/types'; // Keep enum imports without 'type'
import { Database } from '@/types/supabase';

// Use database types for activities
type ActivityRow = Database['public']['Tables']['activities']['Row'];

// Activity mocks - using real database structure
export const mockActivities: ActivityRow[] = [
  {
    id: '1',
    title: 'Introduction to Pottery',
    description: 'Learn the basics of pottery making.',
    start_date: '2024-07-20',
    end_date: '2024-07-20',
    location: 'Art Studio A',
    capacity: 15,
    type: 'workshop',
    created_at: '2024-07-01T09:00:00Z',
    updated_at: '2024-07-01T09:00:00Z',
    created_by: null,
    image_url: null,
    status: 'PUBLISHED',
    tags: null,
    metadata: null,
    festival_id: null,
    parent_activity_id: null,
    is_featured: null
  },
  {
    id: '2',
    title: 'Tech Enthusiasts Meetup',
    description: 'Discuss the latest trends in technology.',
    start_date: '2024-07-21',
    end_date: '2024-07-21',
    location: 'Community Hall',
    capacity: 50,
    type: 'meetup',
    created_at: '2024-07-02T14:00:00Z',
    updated_at: '2024-07-02T14:00:00Z',
    created_by: null,
    image_url: null,
    status: 'PUBLISHED',
    tags: null,
    metadata: null,
    festival_id: null,
    parent_activity_id: null,
    is_featured: null
  },
  {
    id: '3',
    title: 'Advanced JavaScript Techniques',
    description: 'Deepen your yoga practice with advanced techniques.',
    start_date: '2024-07-22',
    end_date: '2024-07-22',
    location: 'Wellness Center',
    capacity: 20,
    type: 'workshop',
    event_id: '1',
    created_at: '2024-07-03T11:00:00Z',
    updated_at: '2024-07-03T11:00:00Z',
    created_by: null,
    image_url: null,
    requirements: null,
    max_participants: 20,
    status: 'PUBLISHED'
  },
  {
    id: '4',
    title: 'Book Club Meeting',
    description: 'Discussing this month\'s selected novel.',
    start_date: '2024-07-23',
    end_date: '2024-07-23',
    location: 'Library Reading Room',
    capacity: 25,
    type: 'meetup',
    event_id: '1',
    created_at: '2024-07-04T16:00:00Z',
    updated_at: '2024-07-04T16:00:00Z',
    created_by: null,
    image_url: null,
    requirements: null,
    max_participants: 25,
    status: 'PUBLISHED'
  },
];

// Event types and mocks
interface MockEvent {
  id: string;
  title: string;
  description: string;
  date: string;
  location: string;
  image?: string;
  category: FestivalType;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export const mockEvents: MockEvent[] = [
  {
    id: '1',
    title: 'Summer Music Festival',
    description: 'Three days of amazing music under the sun',
    date: 'July 15-17, 2025',
    location: 'Riverside Park',
    image: '/images/events/summer-fest.jpg',
    category: FestivalType.MUSIC,
    created_by: 'admin',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    title: 'Art in the Park',
    description: 'A celebration of local artists and creativity',
    date: 'August 5, 2025',
    location: 'Central Park',
    image: '/images/events/art-fest.jpg',
    category: FestivalType.ART,
    created_by: 'admin',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '3',
    title: 'Food & Wine Festival',
    description: 'Taste the best local and international cuisine',
    date: 'September 1-2, 2025',
    location: 'Downtown Square',
    image: '/images/events/food-fest.jpg',
    category: FestivalType.FOOD,
    created_by: 'admin',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

export const mockCategories = [
  FestivalType.MUSIC,
  FestivalType.FOOD,
  FestivalType.ART,
  FestivalType.THEATER,
  FestivalType.SPORTS
] as const;

export const mockLocations = [
  'Central Park',
  'Downtown Square',
  'City Gardens',
  'Beach Front',
  'Convention Center'
] as const;

export const profiles: Profile[] = [
  {
    id: '1', // Profile ID seems to be string based on database.types.ts, keeping as string
    username: 'john_doe',
    email: '<EMAIL>',
    full_name: 'John Doe',
    avatar_url: null,
    website: null,
    bio: 'Music lover and festival enthusiast',
    role: 'USER',
    interests: ['music', 'art'],
    location: 'New York, NY',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

export const festivals: Festival[] = [
  {
    id: '1', // Changed back to string UUID
    name: 'Summer Fest 2024',
    description: 'Annual summer festival with music, art, and workshops.',
    start_date: '2024-07-20',
    end_date: '2024-07-25',
    location: 'Central Park',
    website: 'https://summerfest.com',
    image_url: 'https://summerfest.com/images/festival-banner.jpg',
    created_at: '2024-06-01T12:00:00Z',
    updated_at: '2024-06-15T10:30:00Z',
    status: 'PUBLISHED',
    created_by: null,
    featured: false,
  },
];
