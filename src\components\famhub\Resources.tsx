import React from 'react';
import { Resource } from '@/types'; // Import the unified Resource type
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ExternalLinkIcon } from 'lucide-react'; // Or appropriate icon library

// Remove the local ExternalLink and Resource definitions as they are now imported
// type ExternalLink = Database['public']['Tables']['external_links']['Row'];
// interface Resource { ... }

export type ResourcesProps = {
  resources?: Resource[]; // Use the imported unified Resource type
  // externalLinks is no longer needed as they are part of the unified Resource type
  // externalLinks?: Database['public']['Tables']['external_links']['Row'][];
  isLoading?: boolean;
  error?: Error | null;
  className?: string;
};

const Resources: React.FC<ResourcesProps> = ({ resources = [], isLoading = false, error = null, className = '' }) => {

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ${className}`}>
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-300 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return <div className={`text-red-500 ${className}`}>Error loading resources: {error.message}</div>;
  }

  if (!resources || resources.length === 0) {
    return <div className={className}>No resources available at the moment.</div>;
  }

  // Helper to render link or content
  const renderResourceContent = (resource: Resource) => {
    const link = resource.resourceType === 'external_link' ? resource.url : (resource as any).link;
    const content = resource.resourceType !== 'external_link' ? resource.content : null;

    if (link) {
      return (
        <a
          href={link}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline inline-flex items-center"
        >
          Learn More <ExternalLinkIcon className="ml-1 h-4 w-4" />
        </a>
      );
    } else if (content) {
      // Render content directly if no link (e.g., for guides/tips with inline content)
      // Consider adding markdown rendering here if content is markdown
      return <p className="text-sm text-gray-600 mt-2">{content}</p>;
    }
    return null;
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {resources.map((resource) => (
        <Card key={`${resource.resourceType}-${resource.id}`} className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">{resource.title}</CardTitle>
            <span className="text-xs uppercase tracking-wider font-medium text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full self-start">
              {resource.resourceType.replace('_', ' ')}
            </span>
          </CardHeader>
          <CardContent>
            {resource.description && (
              <p className="text-sm text-gray-700 mb-3">{resource.description}</p>
            )}
            {renderResourceContent(resource)}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default Resources;
