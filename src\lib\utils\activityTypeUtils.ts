/**
 * Activity Type Utilities
 * 
 * This utility provides standardized functions for handling activity type conversions
 * and ensuring type consistency between database schema and application types.
 * It addresses the inconsistencies with nullable fields like is_featured and capacity.
 */

import { ActivityItem } from '@/types';
import { ActivityType, normalizeActivityType } from '@/types/enums';
import { Activity } from '@/types/activities';
import { Json, Database } from '@/types/supabase';

/**
 * Ensures consistent typing for is_featured field
 * Handles the inconsistency between database schema (boolean) and application types (boolean | null)
 * 
 * @param value - The is_featured value from any source
 * @returns Properly typed boolean | null value
 */
export function normalizeIsFeatured(value: unknown): boolean | null {
  // If value is explicitly boolean, return it
  if (typeof value === 'boolean') {
    return value;
  }
  
  // If value is null or undefined, return null
  if (value === null || value === undefined) {
    return null;
  }
  
  // For string values (from form inputs, etc.)
  if (typeof value === 'string') {
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;
  }
  
  // Default fallback
  return null;
}

/**
 * Ensures consistent typing for capacity field
 * Handles the inconsistency between database schema (number) and application types (number | null)
 * 
 * @param value - The capacity value from any source
 * @returns Properly typed number | null value
 */
export function normalizeCapacity(value: unknown): number | null {
  // If value is explicitly number, return it
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }
  
  // If value is null or undefined, return null
  if (value === null || value === undefined) {
    return null;
  }
  
  // For string values (from form inputs, etc.)
  if (typeof value === 'string' && value.trim() !== '') {
    const parsed = parseInt(value, 10);
    if (!isNaN(parsed)) return parsed;
  }
  
  // Default fallback
  return null;
}

/**
 * Ensures consistent typing for tags field
 * Handles the inconsistency between database schema (string[]) and application types (string[] | null)
 * 
 * @param value - The tags value from any source
 * @returns Properly typed string[] | null value
 */
export function normalizeTags(value: unknown): string[] | null {
  // If value is explicitly array, return it
  if (Array.isArray(value)) {
    return value.filter(item => typeof item === 'string');
  }
  
  // If value is null or undefined, return null
  if (value === null || value === undefined) {
    return null;
  }
  
  // Default fallback
  return null;
}

/**
 * Ensures consistent typing for metadata field
 * Handles the inconsistency between database schema (Json) and application types (Json | null)
 * 
 * @param value - The metadata value from any source
 * @returns Properly typed Json | null value
 */
export function normalizeMetadata(value: unknown): Json | null {
  // If value is null or undefined, return null
  if (value === null || value === undefined) {
    return null;
  }
  
  // If value is object, return it as Json
  if (typeof value === 'object') {
    return value as Json;
  }
  
  // Default fallback
  return {} as Json;
}

/**
 * Standardizes an ActivityItem to ensure all fields have consistent types
 * This is particularly useful when working with mock data or data from different sources
 * 
 * @param activity - The activity item to standardize
 * @returns A standardized ActivityItem with consistent types
 */
export function standardizeActivityItem(activity: Partial<ActivityItem>): ActivityItem {
  return {
    // Required fields - using correct database schema
    id: activity.id || '', // Keep as string, don't convert to number
    title: activity.title || activity.description || '', // Use title field from database
    description: activity.description || '',
    type: normalizeActivityType(activity.type) as Database["public"]["Enums"]["activity_type"],
    location: activity.location || '',

    // Required fields from Activity interface
    image_url: activity.image_url ?? null,
    status: activity.status ?? null,
    tags: activity.tags ?? null,
    metadata: activity.metadata ?? null,
    festival_id: activity.festival_id ?? null,
    is_featured: activity.is_featured ?? null,
    created_by: activity.created_by ?? null,

    // Nullable fields with proper type handling - using actual database schema
    start_date: activity.start_date ?? null, // Use start_date from database schema
    end_date: activity.end_date ?? null, // Use end_date from database schema
    capacity: normalizeCapacity(activity.capacity),
    parent_activity_id: activity.parent_activity_id ?? null,
    created_at: activity.created_at ?? null,
    updated_at: activity.updated_at || null,

    // Optional backward compatibility fields
    ...(activity.date ? { date: activity.date } : {}),
    ...(activity.time ? { time: activity.time } : {}),
    ...(activity.longDescription ? { longDescription: activity.longDescription } : {}),
    ...(activity.participants ? { participants: activity.participants } : {}),
  };
}

/**
 * Converts an ActivityItem to the Activity interface from activities.ts
 * Ensures consistent type handling between the two interfaces
 * 
 * @param activityItem - The ActivityItem to convert
 * @returns An Activity object with consistent types
 */
export function convertToActivity(activityItem: ActivityItem): Activity {
  return {
    id: activityItem.id.toString(), // Convert number to string for Activity type
    title: activityItem.description ?? '', // Use description as title since title field doesn't exist
    description: activityItem.description || '',
    type: normalizeActivityType(activityItem.type),
    location: activityItem.location || '',
    image_url: null, // ActivityItem doesn't have image_url field
    start_date: activityItem.start_date || null, // Use start_date from database schema
    end_date: activityItem.end_date || null, // Use end_date from database schema
    capacity: normalizeCapacity(activityItem.capacity),
    status: null, // ActivityItem doesn't have status field
    tags: null, // ActivityItem doesn't have tags field
    metadata: null, // ActivityItem doesn't have metadata field
    festival_id: null, // festival_id field doesn't exist in ActivityItem
    parent_activity_id: activityItem.parent_activity_id?.toString() || null, // Convert number to string
    is_featured: null, // ActivityItem doesn't have is_featured field
    created_by: null, // ActivityItem doesn't have created_by field
    created_at: activityItem.created_at,
    updated_at: activityItem.updated_at
  };
}