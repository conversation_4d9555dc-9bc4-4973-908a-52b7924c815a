import { supabase } from '../client';
import { BaseService, ServiceResponse } from './base-service';

export interface ActivityParticipant {
  id: string;
  user_id: string;
  activity_id: string; // FIXED: UUID instead of number
  status: 'registered' | 'attended' | 'cancelled';
  registration_date: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface UserFavorite {
  id: string;
  user_id: string;
  activity_id: string; // FIXED: UUID instead of number
  created_at: string;
}

export interface ActivityView {
  id: string;
  user_id?: string;
  activity_id: string; // FIXED: UUID instead of number
  viewed_at: string;
  session_id?: string;
  ip_address?: string;
}

export class UserInteractionsService extends BaseService {
  /**
   * Join an activity
   */
  async joinActivity(activityId: string, userId: string, notes?: string): Promise<ServiceResponse<ActivityParticipant>> {
    try {
      // First, create the tables if they don't exist
      await this.ensureTablesExist();

      const { data, error } = await this.client
        .from('activity_participants')
        .insert({
          user_id: userId,
          activity_id: activityId,
          status: 'registered',
          notes: notes || null,
          registration_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null, status: 'success' };
    } catch (error) {
      console.error('Error joining activity:', error);
      return { data: null, error: error as Error, status: 'error' };
    }
  }

  /**
   * Leave an activity
   */
  async leaveActivity(activityId: string, userId: string): Promise<ServiceResponse<null>> {
    try {
      const { error } = await this.client
        .from('activity_participants')
        .delete()
        .eq('user_id', userId)
        .eq('activity_id', activityId);

      if (error) throw error;
      return { data: null, error: null, status: 'success' };
    } catch (error) {
      console.error('Error leaving activity:', error);
      return { data: null, error: error as Error, status: 'error' };
    }
  }

  /**
   * Check if user has joined an activity
   */
  async isUserJoined(activityId: string, userId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await this.client
        .from('activity_participants')
        .select('id')
        .eq('user_id', userId)
        .eq('activity_id', activityId)
        .eq('status', 'registered')
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
      return { data: !!data, error: null, status: 'success' };
    } catch (error) {
      console.error('Error checking join status:', error);
      return { data: false, error: error as Error, status: 'error' };
    }
  }

  /**
   * Add activity to favorites
   */
  async addToFavorites(activityId: string, userId: string): Promise<ServiceResponse<UserFavorite>> {
    try {
      await this.ensureTablesExist();

      const { data, error } = await this.client
        .from('user_favorites')
        .insert({
          user_id: userId,
          activity_id: activityId,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null, status: 'success' };
    } catch (error) {
      console.error('Error adding to favorites:', error);
      return { data: null, error: error as Error, status: 'error' };
    }
  }

  /**
   * Remove activity from favorites
   */
  async removeFromFavorites(activityId: string, userId: string): Promise<ServiceResponse<null>> {
    try {
      const { error } = await this.client
        .from('user_favorites')
        .delete()
        .eq('user_id', userId)
        .eq('activity_id', activityId);

      if (error) throw error;
      return { data: null, error: null, status: 'success' };
    } catch (error) {
      console.error('Error removing from favorites:', error);
      return { data: null, error: error as Error, status: 'error' };
    }
  }

  /**
   * Check if activity is favorited by user
   */
  async isFavorited(activityId: string, userId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await this.client
        .from('user_favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('activity_id', activityId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return { data: !!data, error: null, status: 'success' };
    } catch (error) {
      console.error('Error checking favorite status:', error);
      return { data: false, error: error as Error, status: 'error' };
    }
  }

  /**
   * Record activity view
   */
  async recordView(activityId: string, userId?: string, sessionId?: string): Promise<ServiceResponse<ActivityView>> {
    try {
      await this.ensureTablesExist();

      const { data, error } = await this.client
        .from('activity_views')
        .insert({
          user_id: userId || null,
          activity_id: activityId,
          viewed_at: new Date().toISOString(),
          session_id: sessionId || null
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null, status: 'success' };
    } catch (error) {
      console.error('Error recording view:', error);
      return { data: null, error: error as Error, status: 'error' };
    }
  }

  /**
   * Get user's participations
   */
  async getUserParticipations(userId: string): Promise<ServiceResponse<ActivityParticipant[]>> {
    try {
      const { data, error } = await this.client
        .from('activity_participants')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data: data || [], error: null, status: 'success' };
    } catch (error) {
      console.error('Error getting user participations:', error);
      return { data: [], error: error as Error, status: 'error' };
    }
  }

  /**
   * Get user's favorites
   */
  async getUserFavorites(userId: string): Promise<ServiceResponse<UserFavorite[]>> {
    try {
      const { data, error } = await this.client
        .from('user_favorites')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data: data || [], error: null, status: 'success' };
    } catch (error) {
      console.error('Error getting user favorites:', error);
      return { data: [], error: error as Error, status: 'error' };
    }
  }



  /**
   * Ensure required tables exist (creates them if missing)
   */
  private async ensureTablesExist(): Promise<void> {
    try {
      // This will attempt to create tables if they don't exist
      // We'll handle errors gracefully since tables might already exist
      
      // Try to query each table to see if it exists
      await this.client.from('activity_participants').select('id').limit(1);
      await this.client.from('user_favorites').select('id').limit(1);
      await this.client.from('activity_views').select('id').limit(1);
    } catch (error) {
      // Tables don't exist, but we can't create them without proper permissions
      // This will be handled by the database admin or migration scripts
      console.warn('User interaction tables may not exist yet. Please run database migrations.');
    }
  }
}

// Export singleton instance
export const userInteractionsService = new UserInteractionsService();
