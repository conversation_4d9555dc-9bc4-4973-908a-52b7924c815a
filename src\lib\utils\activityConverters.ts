/**
 * Activity Type Converters
 * 
 * This utility provides functions to convert between Supabase database types
 * and our application-specific activity types with specialized subtypes.
 */

import { Database } from '@/types/supabase';
import { ActivityTypeEnum, normalizeActivityType } from '@/types/activityTypes'; // Corrected path
import { ActivityItem } from '@/types'; // Corrected path
import { Activity, Meetup, Workshop, ActivityWithDetails } from '@/types/activities'; // Corrected path

// Type aliases from Supabase (Uncommented now that placeholders exist)
export type SupabaseActivity = Database['public']['Tables']['activities']['Row'];
export type SupabaseMeetup = Database['public']['Tables']['meetups']['Row'];
export type SupabaseWorkshop = Database['public']['Tables']['workshops']['Row'];

/**
 * Converts a Supabase activity to our internal Activity type
 * Simplified to handle the standardized ActivityItem interface
 */
import { normalizeIsFeatured, normalizeCapacity, normalizeTags, normalizeMetadata } from './activityTypeUtils';

/**
 * Converts a Supabase activity or ActivityItem to our internal Activity type
 * Uses standardized normalization functions to ensure consistent type handling
 * Simplified based on TECH_DEBT.md recommendations and removal of legacy fields from mocks.
 */
export const convertActivity = (activity: SupabaseActivity | ActivityItem): Activity => { // Re-added SupabaseActivity type
  // Standardize common fields using utility functions
  const standardized = {
    // Required fields
    id: activity.id,
    title: activity.description || '', // Use description as title since title field doesn't exist
    description: activity.description,
    location: activity.location,
    type: normalizeActivityType(activity.type) as any, // Use unified type normalization
    
    // Nullable fields with standardized type handling
    image_url: null, // image_url field doesn't exist in ActivityItem
    start_date: activity.start_date ?? null, // Use start_date from database schema
    end_date: activity.end_date ?? null, // Use end_date from database schema
    capacity: normalizeCapacity(activity.capacity),
    status: null, // status field doesn't exist in ActivityItem
    tags: null, // tags field doesn't exist in ActivityItem
    metadata: null, // metadata field doesn't exist in ActivityItem
    festival_id: null, // festival_id field doesn't exist in ActivityItem
    parent_activity_id: activity.parent_activity_id ?? null,
    is_featured: null, // is_featured field doesn't exist in ActivityItem
    created_at: activity.created_at ?? null,
    updated_at: activity.updated_at ?? null,
    created_by: null // created_by field doesn't exist in ActivityItem
  };
  
  return standardized;
};

/**
 * Converts a Supabase meetup to our internal Meetup type
 */
export const convertMeetup = (meetup: SupabaseMeetup): Meetup => {
  return {
    id: meetup.id,
    activity_id: meetup.activity_id,
    organizer_id: meetup.organizer_id ?? null,
    max_participants: meetup.max_participants ?? null,
    location_details: meetup.location_details ?? null,
    prerequisites: meetup.prerequisites ?? null,
    is_public: meetup.is_public ?? true,
    created_at: meetup.created_at ?? new Date().toISOString(),
    updated_at: meetup.updated_at ?? new Date().toISOString()
  };
};

/**
 * Converts a Supabase workshop to our internal Workshop type
 */
export const convertWorkshop = (workshop: SupabaseWorkshop): Workshop => {
  return {
    id: workshop.id,
    activity_id: workshop.activity_id,
    instructor_id: workshop.instructor_id ?? null,
    materials_needed: workshop.materials_needed ?? null,
    skill_level: workshop.skill_level ?? null,
    duration_minutes: workshop.duration_minutes ?? null,
    created_at: workshop.created_at ?? new Date().toISOString(),
    updated_at: workshop.updated_at ?? new Date().toISOString()
  };
};

/**
 * Combines an activity with its specialized details (meetup or workshop)
 */
export const combineActivityWithDetails = (
  activity: Activity,
  meetup?: Meetup,
  workshop?: Workshop
): ActivityWithDetails => {
  return {
    ...activity,
    meetup,
    workshop
  };
};

/**
 * Fetches activities with their specialized details
 * 
 * Example usage with Supabase:
 * 
 * ```typescript
 * const { data: activities } = await supabase
 *   .from('activities')
 *   .select('*')
 *   .eq('festival_id', festivalId);
 * 
 * const { data: meetups } = await supabase
 *   .from('meetups')
 *   .select('*')
 *   .in('activity_id', activities.map(a => a.id));
 * 
 * const { data: workshops } = await supabase
 *   .from('workshops')
 *   .select('*')
 *   .in('activity_id', activities.map(a => a.id));
 * 
 * const activitiesWithDetails = combineActivitiesWithDetails(
 *   activities,
 *   meetups,
 *   workshops
 * );
 * ```
 */
/**
 * Combines activities with their specialized details
 * Ensures consistent type handling for all activities and their details
 * Simplified based on TECH_DEBT.md recommendations and removal of legacy fields from mocks.
 */
export const combineActivitiesWithDetails = (
  activities: (SupabaseActivity | ActivityItem)[], // Re-added SupabaseActivity type
  meetups: SupabaseMeetup[] = [],
  workshops: SupabaseWorkshop[] = []
): ActivityWithDetails[] => {
  // Convert all activities using the simplified convertActivity function
  const convertedActivities = activities.map(convertActivity);
  
  // Create lookup maps for meetups and workshops by activity_id
  const meetupMap = new Map<string, Meetup>();
  meetups.forEach(m => {
    meetupMap.set(m.activity_id, convertMeetup(m));
  });
  
  const workshopMap = new Map<string, Workshop>();
  workshops.forEach(w => {
    workshopMap.set(w.activity_id, convertWorkshop(w));
  });
  
  // Combine activities with their specialized details
  return convertedActivities.map(activity => {
    return combineActivityWithDetails(
      activity,
      meetupMap.get(activity.id),
      workshopMap.get(activity.id)
    );
  });
}