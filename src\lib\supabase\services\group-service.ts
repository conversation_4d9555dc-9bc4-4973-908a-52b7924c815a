/**
 * Group Service
 * 
 * This service handles group-related operations like creating groups,
 * managing members, and group activities.
 */

import { BaseService, ServiceResponse } from './base-service'
import type { Group } from '@/types'

/**
 * Group service for handling group operations
 */
export class GroupService extends BaseService {
  /**
   * Create a new group with enhanced activity-based features
   * @param name - Group name
   * @param description - Group description
   * @param creatorId - ID of the user creating the group
   * @param festivalId - Optional ID of the associated festival
   * @param options - Additional group options
   * @returns ServiceResponse with the created group
   */
  async createGroup(
    name: string,
    description: string,
    creatorId: string,
    festivalId?: string,
    options: {
      isPrivate?: boolean
      formationType?: 'manual' | 'activity_based' | 'music_based' | 'hybrid' | 'spontaneous'
      maxMembers?: number
      activityFocus?: string[]
      musicFocus?: string[]
      tags?: string[]
      expiresInHours?: number
    } = {}
  ): Promise<ServiceResponse<any>> {
    return this.handleResponse(
      (async () => {
        const {
          isPrivate = false,
          formationType = 'manual',
          maxMembers = 20,
          activityFocus = [],
          musicFocus = [],
          tags = [],
          expiresInHours
        } = options

        const expiresAt = expiresInHours
          ? new Date(Date.now() + expiresInHours * 60 * 60 * 1000).toISOString()
          : null

        // Create the group with enhanced fields
        const { data: group, error: groupError } = await this.client
          .from('groups')
          .insert({
            name,
            description,
            creator_id: creatorId,
            festival_id: festivalId,
            is_private: isPrivate,
            formation_type: formationType,
            max_members: maxMembers,
            activity_focus: activityFocus,
            music_focus: musicFocus,
            tags,
            expires_at: expiresAt,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (groupError) throw groupError;
        if (!group) throw new Error('Failed to create group');

        // Add the creator as a member with admin role
        const { error: memberError } = await this.client
          .from('group_members')
          .insert({
            group_id: group.id,
            user_id: creatorId,
            role: 'admin',
            joined_at: new Date().toISOString()
          });

        if (memberError) throw memberError;

        return group;
      })()
    );
  }
  
  /**
   * Get a group by ID
   * @param groupId - The group ID
   * @returns ServiceResponse with the group data
   */
  async getGroup(groupId: string): Promise<ServiceResponse<any>> {
    return this.handleResponse(
      this.client
        .from('groups')
        .select(`
          *,
          profiles!groups_creator_id_fkey(id, username, full_name, avatar_url),
          festivals!groups_festival_id_fkey(id, name, start_date, end_date, location)
        `)
        .eq('id', groupId)
        .single()
    );
  }
  
  /**
   * Update a group
   * @param groupId - The group ID
   * @param updates - The updates to apply
   * @returns ServiceResponse with the updated group
   */
  async updateGroup(
    groupId: string,
    updates: {
      name?: string;
      description?: string;
      festival_id?: string;
      is_private?: boolean;
    }
  ): Promise<ServiceResponse<any>> {
    return this.handleResponse(
      this.client
        .from('groups')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', groupId)
        .select()
        .single()
    );
  }
  
  /**
   * Delete a group
   * @param groupId - The group ID
   * @returns ServiceResponse with success status
   */
  async deleteGroup(groupId: string): Promise<ServiceResponse<null>> {
    return this.handleResponse(
      this.client
        .from('groups')
        .delete()
        .eq('id', groupId)
    );
  }
  
  /**
   * Add a member to a group
   * @param groupId - The group ID
   * @param userId - The user ID
   * @param role - The member's role
   * @returns ServiceResponse with the created group member
   */
  async addGroupMember(
    groupId: string,
    userId: string,
    role = 'member'
  ): Promise<ServiceResponse<any>> {
    return this.handleResponse(
      this.client
        .from('group_members')
        .insert({
          group_id: groupId,
          user_id: userId,
          role,
          joined_at: new Date().toISOString()
        })
        .select()
        .single()
    );
  }
  
  /**
   * Remove a member from a group
   * @param groupId - The group ID
   * @param userId - The user ID
   * @returns ServiceResponse with success status
   */
  async removeGroupMember(
    groupId: string,
    userId: string
  ): Promise<ServiceResponse<null>> {
    return this.handleResponse(
      this.client
        .from('group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', userId)
    );
  }
  
  /**
   * Get all members of a group
   * @param groupId - The group ID
   * @returns ServiceResponse with the group members
   */
  async getGroupMembers(groupId: string): Promise<ServiceResponse<any[]>> {
    return this.handleResponse(
      this.client
        .from('group_members')
        .select(`
          role,
          joined_at,
          profiles!group_members_user_id_fkey(
            id,
            username,
            full_name,
            avatar_url,
            bio
          )
        `)
        .eq('group_id', groupId)
    );
  }
  
  /**
   * Get all groups a user is a member of
   * @param userId - The user ID
   * @returns ServiceResponse with the user's groups
   */
  async getUserGroups(userId: string): Promise<ServiceResponse<any[]>> {
    return this.handleResponse(
      this.client
        .from('group_members')
        .select(`
          role,
          joined_at,
          groups!group_members_group_id_fkey(
            id,
            name,
            description,
            is_private,
            created_at,
            updated_at,
            festival_id,
            festivals!groups_festival_id_fkey(
              id,
              name,
              start_date,
              end_date,
              location
            )
          )
        `)
        .eq('user_id', userId)
    );
  }
  
  /**
   * Search for groups
   * @param query - Search query
   * @param festivalId - Optional festival ID to filter by
   * @param limit - Maximum number of results
   * @returns ServiceResponse with matching groups
   */
  async searchGroups(
    query: string,
    festivalId?: string,
    limit = 10
  ): Promise<ServiceResponse<any[]>> {
    let dbQuery = this.client
      .from('groups')
      .select(`
        *,
        profiles!groups_creator_id_fkey(id, username, full_name, avatar_url),
        festivals!groups_festival_id_fkey(id, name, start_date, end_date, location)
      `)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_private', false)
      .limit(limit);
    
    if (festivalId) {
      dbQuery = dbQuery.eq('festival_id', festivalId);
    }
    
    return this.handleResponse(dbQuery);
  }

  /**
   * Get activity-based group recommendations for a user
   */
  async getActivityBasedGroupRecommendations(
    userId: string,
    festivalId: string,
    limit = 5
  ): Promise<ServiceResponse<any[]>> {
    return this.handleResponse(
      (async () => {
        // Get user's activity attendance
        const { data: userActivities, error: activitiesError } = await this.client
          .from('activity_attendance')
          .select(`
            activity_id,
            activities!activity_attendance_activity_id_fkey(title, type)
          `)
          .eq('user_id', userId)
          .eq('status', 'going')

        if (activitiesError) throw activitiesError

        const activityTypes = userActivities?.map(a => (a as any).activities?.type).filter(Boolean) || []
        const activityTitles = userActivities?.map(a => (a as any).activities?.title).filter(Boolean) || []

        // Find groups with similar activity focus
        let query = this.client
          .from('groups')
          .select(`
            *,
            profiles!groups_creator_id_fkey(id, username, full_name, avatar_url)
          `)
          .eq('festival_id', festivalId)
          .eq('is_active', true)
          .eq('is_private', false)

        if (activityTitles.length > 0) {
          query = query.overlaps('activity_focus', activityTitles)
        }

        const { data: groups, error: groupsError } = await query.limit(limit)

        if (groupsError) throw groupsError

        return groups || []
      })()
    )
  }

  /**
   * Get music-based group recommendations for a user
   */
  async getMusicBasedGroupRecommendations(
    userId: string,
    festivalId: string,
    limit = 5
  ): Promise<ServiceResponse<any[]>> {
    return this.handleResponse(
      (async () => {
        // Get user's music preferences
        const { data: musicPrefs, error: musicError } = await this.client
          .from('artist_preferences')
          .select('artist_name, genre')
          .eq('user_id', userId)
          .in('preference_level', ['love', 'like'])

        if (musicError) throw musicError

        const artists = musicPrefs?.map(p => p.artist_name).filter(Boolean) || []
        const genres = musicPrefs?.map(p => p.genre).filter(Boolean) || []

        // Find groups with similar music focus
        let query = this.client
          .from('groups')
          .select(`
            *,
            profiles!groups_creator_id_fkey(id, username, full_name, avatar_url)
          `)
          .eq('festival_id', festivalId)
          .eq('is_active', true)
          .eq('is_private', false)

        if (artists.length > 0) {
          query = query.overlaps('music_focus', artists)
        }

        const { data: groups, error: groupsError } = await query.limit(limit)

        if (groupsError) throw groupsError

        return groups || []
      })()
    )
  }

  /**
   * Add activities to a group's focus
   */
  async addGroupActivities(
    groupId: string,
    activityIds: string[],
    addedBy: string
  ): Promise<ServiceResponse<any[]>> {
    return this.handleResponse(
      (async () => {
        const insertData = activityIds.map(activityId => ({
          group_id: groupId,
          activity_id: activityId,
          added_by: addedBy,
          is_primary: false
        }))

        const { data, error } = await this.client
          .from('group_activities')
          .upsert(insertData)
          .select()

        if (error) throw error

        return data || []
      })()
    )
  }

  /**
   * Get active spontaneous groups for a festival
   * @param festivalId - Festival ID
   * @returns Promise<ServiceResponse<Group[]>>
   */
  async getActiveSpontaneousGroups(festivalId: string): Promise<ServiceResponse<Group[]>> {
    return this.handleResponse(
      (async () => {
        const { data, error } = await this.client
          .from('groups')
          .select(`
            *,
            group_members!inner(user_id, role, joined_at),
            profiles!group_members(id, username, full_name, avatar_url)
          `)
          .eq('festival_id', festivalId)
          .eq('formation_type', 'spontaneous')
          .eq('is_active', true)
          .order('created_at', { ascending: false })

        if (error) throw error

        return data || []
      })()
    )
  }
}

// Create and export a singleton instance
export const groupService = new GroupService();
