/**
 * Activity Attendance Service
 * 
 * Handles activity attendance tracking, buddy finding, and real-time coordination.
 * Follows Festival Family's established service patterns and error handling.
 * 
 * @module ActivityAttendanceService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from './base-service'
import type { 
  ActivityAttendance, 
  AttendanceStatus, 
  ActivityWithAttendance,
  SetAttendanceRequest,
  FindActivityBuddiesRequest,
  ActivityCoordinationSuggestion,
  LiveActivityStatus
} from '../activity-coordination/types'
import type { Profile } from '../database.types'

/**
 * Service for managing activity attendance and coordination
 */
export class ActivityAttendanceService extends BaseService {
  
  /**
   * Set or update user's attendance status for an activity
   */
  async setAttendance(
    userId: string, 
    activityId: string, 
    status: AttendanceStatus,
    notes?: string
  ): Promise<ServiceResponse<ActivityAttendance>> {
    return this.handleResponse(
      this.client
        .from('activity_attendance')
        .upsert({
          user_id: userId,
          activity_id: activityId,
          status,
          notes,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
    );
  }

  /**
   * Get user's attendance status for a specific activity
   */
  async getUserAttendance(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<ActivityAttendance | null>> {
    return this.handleResponse(
      this.client
        .from('activity_attendance')
        .select('*')
        .eq('user_id', userId)
        .eq('activity_id', activityId)
        .maybeSingle()
    );
  }

  /**
   * Get all users attending an activity with optional status filter
   */
  async getActivityAttendees(
    activityId: string,
    status?: AttendanceStatus
  ): Promise<ServiceResponse<Profile[]>> {
    let query = this.client
      .from('activity_attendance')
      .select(`
        *,
        profiles!activity_attendance_user_id_fkey(
          id,
          username,
          full_name,
          avatar_url,
          bio,
          interests
        )
      `)
      .eq('activity_id', activityId);

    if (status) {
      query = query.eq('status', status);
    }

    return this.handleResponse(
      (async () => {
        const { data, error } = await query;
        if (error) throw error;
        
        // Extract profiles from the joined data
        return data?.map(item => item.profiles).filter(Boolean) || [];
      })()
    );
  }

  /**
   * Get activity with attendance data and counts
   */
  async getActivityWithAttendance(
    activityId: string,
    currentUserId?: string
  ): Promise<ServiceResponse<ActivityWithAttendance>> {
    return this.handleResponse(
      (async () => {
        // Get activity details
        const { data: activity, error: activityError } = await this.client
          .from('activities')
          .select('*')
          .eq('id', activityId)
          .single();

        if (activityError) throw activityError;

        // Get attendance data
        const { data: attendance, error: attendanceError } = await this.client
          .from('activity_attendance')
          .select(`
            *,
            profiles!activity_attendance_user_id_fkey(
              id,
              username,
              full_name,
              avatar_url
            )
          `)
          .eq('activity_id', activityId);

        if (attendanceError) throw attendanceError;

        // Get attendance counts
        const { data: counts, error: countsError } = await this.client
          .rpc('get_activity_attendance_counts', { activity_uuid: activityId });

        if (countsError) throw countsError;

        // Find current user's attendance
        const userAttendance = currentUserId 
          ? attendance?.find(a => a.user_id === currentUserId)
          : undefined;

        // Build enhanced activity object
        const activityWithAttendance: ActivityWithAttendance = {
          ...activity,
          attendance: attendance || [],
          user_attendance: userAttendance,
          attendance_counts: counts?.[0] ? {
            going: Number(counts[0].going_count),
            interested: Number(counts[0].interested_count),
            maybe: Number(counts[0].maybe_count),
            total: Number(counts[0].total_count)
          } : { going: 0, interested: 0, maybe: 0, total: 0 },
          attendees: attendance?.map(a => a.profiles).filter(Boolean) || []
        };

        return activityWithAttendance;
      })()
    );
  }

  /**
   * Find activity buddies - users going to the same activity
   */
  async findActivityBuddies(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<Profile[]>> {
    return this.handleResponse(
      (async () => {
        const { data, error } = await this.client
          .from('activity_attendance')
          .select(`
            profiles!activity_attendance_user_id_fkey(
              id,
              username,
              full_name,
              avatar_url,
              bio,
              interests
            )
          `)
          .eq('activity_id', activityId)
          .eq('status', 'going')
          .neq('user_id', userId);

        if (error) throw error;

        return data?.map(item => item.profiles).filter(Boolean) || [];
      })()
    );
  }

  /**
   * Get user's activity schedule (all activities they're attending)
   */
  async getUserActivitySchedule(
    userId: string,
    festivalId?: string,
    statusFilter?: AttendanceStatus[]
  ): Promise<ServiceResponse<ActivityWithAttendance[]>> {
    return this.handleResponse(
      (async () => {
        let query = this.client
          .from('activity_attendance')
          .select(`
            *,
            activities!activity_attendance_activity_id_fkey(*)
          `)
          .eq('user_id', userId);

        if (statusFilter && statusFilter.length > 0) {
          query = query.in('status', statusFilter);
        } else {
          query = query.in('status', ['going', 'interested']);
        }

        if (festivalId) {
          query = query.eq('activities.festival_id', festivalId);
        }

        const { data, error } = await query.order('activities.start_date', { ascending: true });

        if (error) throw error;

        // Transform to ActivityWithAttendance format
        const activities: ActivityWithAttendance[] = data?.map(item => ({
          ...item.activities,
          user_attendance: {
            id: item.id,
            user_id: item.user_id,
            activity_id: item.activity_id,
            status: item.status,
            notes: item.notes,
            created_at: item.created_at,
            updated_at: item.updated_at
          }
        })) || [];

        return activities;
      })()
    );
  }

  /**
   * Get activity coordination suggestions for a user
   */
  async getActivitySuggestions(
    userId: string,
    festivalId: string,
    limit = 10
  ): Promise<ServiceResponse<ActivityCoordinationSuggestion[]>> {
    return this.handleResponse(
      (async () => {
        // This would typically call a more complex RPC function
        // For now, we'll implement basic logic
        
        // Get user's interests and current attendance
        const { data: userProfile, error: profileError } = await this.client
          .from('profiles')
          .select('interests')
          .eq('id', userId)
          .single();

        if (profileError) throw profileError;

        const { data: userAttendance, error: attendanceError } = await this.client
          .from('activity_attendance')
          .select('activity_id, status')
          .eq('user_id', userId);

        if (attendanceError) throw attendanceError;

        // Get activities in the festival that user isn't attending
        const attendingActivityIds = userAttendance?.map(a => a.activity_id) || [];
        
        let query = this.client
          .from('activities')
          .select('*')
          .eq('festival_id', festivalId);

        if (attendingActivityIds.length > 0) {
          query = query.not('id', 'in', `(${attendingActivityIds.join(',')})`);
        }

        const { data: activities, error: activitiesError } = await query.limit(limit);

        if (activitiesError) throw activitiesError;

        // Create basic suggestions (this would be more sophisticated in production)
        const suggestions: ActivityCoordinationSuggestion[] = activities?.map(activity => ({
          id: `suggestion_${activity.id}`,
          type: 'activity_buddy' as const,
          title: `Join ${activity.title}`,
          description: `Find others interested in ${activity.title}`,
          activity: activity as ActivityWithAttendance,
          suggested_users: [], // Would be populated with matching users
          reason: 'Based on your interests',
          confidence_score: 0.7
        })) || [];

        return suggestions;
      })()
    );
  }

  /**
   * Remove user's attendance from an activity
   */
  async removeAttendance(
    userId: string,
    activityId: string
  ): Promise<ServiceResponse<boolean>> {
    return this.handleResponse(
      (async () => {
        const { error } = await this.client
          .from('activity_attendance')
          .delete()
          .eq('user_id', userId)
          .eq('activity_id', activityId);

        if (error) throw error;
        return true;
      })()
    );
  }

  /**
   * Get live activity status for real-time coordination
   */
  async getLiveActivityStatus(
    activityId: string
  ): Promise<ServiceResponse<LiveActivityStatus>> {
    return this.handleResponse(
      (async () => {
        // Get current attendees
        const { data: attendees, error: attendeesError } = await this.getActivityAttendees(activityId, 'going');
        
        if (attendeesError) throw attendeesError;

        // Build live status object
        const liveStatus: LiveActivityStatus = {
          activity_id: activityId,
          current_attendees: attendees || [],
          looking_for_buddies: [], // Would be filtered based on notes or preferences
          forming_groups: [], // Would come from group formation logic
          real_time_updates: [] // Would come from real-time subscriptions
        };

        return liveStatus;
      })()
    );
  }
}

// Export singleton instance
export const activityAttendanceService = new ActivityAttendanceService();
