/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility utilities for WCAG 2.1 AA compliance
 * including screen reader support, keyboard navigation, and color contrast validation.
 */

import { useEffect, useRef, useCallback, useState } from 'react';

// Color contrast utilities
export const calculateContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (color: string): number => {
    const rgb = color.match(/\d+/g);
    if (!rgb) return 0;
    
    const [r, g, b] = rgb.map(c => {
      const val = parseInt(c) / 255;
      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

export const meetsWCAGContrast = (color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean => {
  const ratio = calculateContrastRatio(color1, color2);
  return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
};

// Screen reader utilities
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

// Focus management
export const useFocusManagement = () => {
  const focusableElements = useRef<HTMLElement[]>([]);
  const currentFocusIndex = useRef(0);

  const getFocusableElements = useCallback((container?: HTMLElement) => {
    const selector = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');

    const elements = Array.from(
      (container || document).querySelectorAll(selector)
    ) as HTMLElement[];

    focusableElements.current = elements;
    return elements;
  }, []);

  const focusFirst = useCallback(() => {
    const elements = getFocusableElements();
    if (elements.length > 0) {
      elements[0].focus();
      currentFocusIndex.current = 0;
    }
  }, [getFocusableElements]);

  const focusLast = useCallback(() => {
    const elements = getFocusableElements();
    if (elements.length > 0) {
      const lastIndex = elements.length - 1;
      elements[lastIndex].focus();
      currentFocusIndex.current = lastIndex;
    }
  }, [getFocusableElements]);

  const focusNext = useCallback(() => {
    const elements = getFocusableElements();
    if (elements.length > 0) {
      currentFocusIndex.current = (currentFocusIndex.current + 1) % elements.length;
      elements[currentFocusIndex.current].focus();
    }
  }, [getFocusableElements]);

  const focusPrevious = useCallback(() => {
    const elements = getFocusableElements();
    if (elements.length > 0) {
      currentFocusIndex.current = currentFocusIndex.current === 0 
        ? elements.length - 1 
        : currentFocusIndex.current - 1;
      elements[currentFocusIndex.current].focus();
    }
  }, [getFocusableElements]);

  return {
    getFocusableElements,
    focusFirst,
    focusLast,
    focusNext,
    focusPrevious
  };
};

// Keyboard navigation hook
export const useKeyboardNavigation = (
  onEscape?: () => void,
  onEnter?: () => void,
  onArrowKeys?: (direction: 'up' | 'down' | 'left' | 'right') => void
) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'Escape':
        onEscape?.();
        break;
      case 'Enter':
        onEnter?.();
        break;
      case 'ArrowUp':
        event.preventDefault();
        onArrowKeys?.('up');
        break;
      case 'ArrowDown':
        event.preventDefault();
        onArrowKeys?.('down');
        break;
      case 'ArrowLeft':
        event.preventDefault();
        onArrowKeys?.('left');
        break;
      case 'ArrowRight':
        event.preventDefault();
        onArrowKeys?.('right');
        break;
    }
  }, [onEscape, onEnter, onArrowKeys]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);
};

// Reduced motion detection
export const useReducedMotion = (): boolean => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = () => setPrefersReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// ARIA utilities
export const generateAriaLabel = (
  baseLabel: string,
  context?: string,
  state?: string
): string => {
  let label = baseLabel;
  if (context) label += `, ${context}`;
  if (state) label += `, ${state}`;
  return label;
};

export const createAriaDescribedBy = (elementId: string, descriptions: string[]): string => {
  return descriptions.map((_, index) => `${elementId}-desc-${index}`).join(' ');
};

// Skip link utilities
export const useSkipLinks = () => {
  const skipLinks = [
    { href: '#main-content', label: 'Skip to main content' },
    { href: '#navigation', label: 'Skip to navigation' },
    { href: '#footer', label: 'Skip to footer' }
  ];

  const renderSkipLinks = () => {
    const container = document.createElement('div');
    container.className = 'skip-links';

    skipLinks.forEach(link => {
      const anchor = document.createElement('a');
      anchor.href = link.href;
      anchor.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 focus:z-50 focus:p-4 focus:bg-blue-600 focus:text-white focus:no-underline';
      anchor.textContent = link.label;
      container.appendChild(anchor);
    });

    return container;
  };

  return { skipLinks, renderSkipLinks };
};

// Touch accessibility
export const useTouchAccessibility = () => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    setIsTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0);
  }, []);

  const getTouchTargetSize = (element: HTMLElement): { width: number; height: number } => {
    const rect = element.getBoundingClientRect();
    return { width: rect.width, height: rect.height };
  };

  const validateTouchTarget = (element: HTMLElement): boolean => {
    const { width, height } = getTouchTargetSize(element);
    return width >= 44 && height >= 44;
  };

  return {
    isTouchDevice,
    getTouchTargetSize,
    validateTouchTarget
  };
};

// High contrast mode detection
export const useHighContrast = (): boolean => {
  const [isHighContrast, setIsHighContrast] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setIsHighContrast(mediaQuery.matches);

    const handleChange = () => setIsHighContrast(mediaQuery.matches);
    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return isHighContrast;
};

// Accessibility audit utilities
export const auditAccessibility = async (): Promise<{
  passed: boolean;
  violations: string[];
  warnings: string[];
}> => {
  const violations: string[] = [];
  const warnings: string[] = [];

  // Check for missing alt text
  const images = document.querySelectorAll('img');
  images.forEach((img, index) => {
    if (!img.alt && !img.getAttribute('aria-label')) {
      violations.push(`Image ${index + 1} missing alt text`);
    }
  });

  // Check for proper heading hierarchy
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let previousLevel = 0;
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > previousLevel + 1) {
      warnings.push(`Heading ${index + 1} skips levels (h${previousLevel} to h${level})`);
    }
    previousLevel = level;
  });

  // Check for touch target sizes
  const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
  interactiveElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      violations.push(`Interactive element ${index + 1} too small (${rect.width}x${rect.height}px)`);
    }
  });

  return {
    passed: violations.length === 0,
    violations,
    warnings
  };
};

// Initialize accessibility features
export const initializeAccessibility = () => {
  // Add skip links
  const skipLinksContainer = document.createElement('div');
  skipLinksContainer.innerHTML = `
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 focus:z-50 focus:p-4 focus:bg-blue-600 focus:text-white">
      Skip to main content
    </a>
  `;
  document.body.insertBefore(skipLinksContainer, document.body.firstChild);

  // Add screen reader styles
  const style = document.createElement('style');
  style.textContent = `
    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }
  `;
  document.head.appendChild(style);
};

export default {
  calculateContrastRatio,
  meetsWCAGContrast,
  announceToScreenReader,
  useFocusManagement,
  useKeyboardNavigation,
  useReducedMotion,
  generateAriaLabel,
  createAriaDescribedBy,
  useSkipLinks,
  useTouchAccessibility,
  useHighContrast,
  auditAccessibility,
  initializeAccessibility
};
