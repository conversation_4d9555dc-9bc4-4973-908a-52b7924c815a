import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { useProfile } from '@/hooks/useProfile';
import { isAdminRole } from '@/lib/utils/auth';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

import { Badge } from '@/components/ui/badge';
import { Pencil, Save, X, Plus, Globe, MessageSquare, Phone, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ContentItem {
  id: string;
  content_key: string;
  content_type: string;
  title: string;
  content: string;
  metadata: any;
  is_active: boolean;
  version: number;
  language: string;
  created_at: string;
  updated_at: string;
}

const contentTypeIcons = {
  hero: Globe,
  marketing: MessageSquare,
  contact: Phone,
  emergency: AlertTriangle,
  page_content: Globe,
  ui_text: MessageSquare
};

const contentTypeColors = {
  hero: 'bg-blue-500/20 text-blue-400',
  marketing: 'bg-green-500/20 text-green-400',
  contact: 'bg-purple-500/20 text-purple-400',
  emergency: 'bg-red-500/20 text-red-400',
  page_content: 'bg-yellow-500/20 text-yellow-400',
  ui_text: 'bg-gray-500/20 text-gray-400'
};

const AdminContentManagement: React.FC = () => {
  const navigate = useNavigate();
  const { profile, loading: profileLoading } = useProfile();
  const [content, setContent] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newContent, setNewContent] = useState({
    content_key: '',
    content_type: 'page_content' as const,
    title: '',
    content: '',
    language: 'en'
  });

  // Redirect non-admin users
  useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [profileLoading, profile, navigate]);

  // Fetch content
  const fetchContent = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('content_management')
        .select('*')
        .order('content_type', { ascending: true })
        .order('content_key', { ascending: true });

      if (error) throw error;
      // Transform the data to match our interface
      const transformedData = (data || []).map(item => ({
        ...item,
        title: item.title || 'Untitled' // Ensure title is never null
      }));
      setContent(transformedData.map(item => ({
        ...item,
        is_active: item.is_active || false,
        version: item.version || 1,
        language: item.language || 'en',
        created_at: item.created_at || new Date().toISOString(),
        updated_at: item.updated_at || new Date().toISOString()
      })));
    } catch (error) {
      console.error('Error fetching content:', error);
      toast.error('Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!profileLoading && isAdminRole(profile?.role)) {
      fetchContent();
    }
  }, [profileLoading, profile]);

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const contentData = {
        content_key: newContent.content_key,
        content_type: newContent.content_type,
        title: newContent.title,
        content: newContent.content,
        language: newContent.language,
        is_active: true,
        version: 1,
        metadata: { editable: true },
        created_by: profile?.id
      };

      const { error } = await supabase
        .from('content_management')
        .insert([contentData]);

      if (error) throw error;

      toast.success('Content created successfully!');
      resetForm();
      fetchContent();
    } catch (error) {
      console.error('Error creating content:', error);
      toast.error('Failed to create content');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdate = async (item: ContentItem) => {
    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('content_management')
        .update({
          title: item.title,
          content: item.content,
          version: item.version + 1
        })
        .eq('id', item.id);

      if (error) throw error;

      toast.success('Content updated successfully!');
      setEditingItem(null);
      fetchContent();
    } catch (error) {
      console.error('Error updating content:', error);
      toast.error('Failed to update content');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleToggleActive = async (item: ContentItem) => {
    try {
      const { error } = await supabase
        .from('content_management')
        .update({ is_active: !item.is_active })
        .eq('id', item.id);

      if (error) throw error;

      toast.success(`Content ${item.is_active ? 'deactivated' : 'activated'} successfully!`);
      fetchContent();
    } catch (error) {
      console.error('Error toggling content status:', error);
      toast.error('Failed to update content status');
    }
  };

  const resetForm = () => {
    setShowCreateForm(false);
    setEditingItem(null);
    setNewContent({
      content_key: '',
      content_type: 'page_content',
      title: '',
      content: '',
      language: 'en'
    });
  };

  const groupedContent = content.reduce((acc, item) => {
    if (!acc[item.content_type]) {
      acc[item.content_type] = [];
    }
    acc[item.content_type].push(item);
    return acc;
  }, {} as Record<string, ContentItem[]>);

  if (profileLoading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Content Management</h1>
          <p className="text-muted-foreground mt-2">
            Manage all user-facing content across the application
          </p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-primary/20 hover:bg-primary/30"
        >
          <Plus className="mr-2 h-4 w-4" />
          Create Content
        </Button>
      </div>

      {/* Create Content Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Content</CardTitle>
            <CardDescription>
              Add new content that will be displayed to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreate} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Content Key *</label>
                  <input
                    type="text"
                    placeholder="e.g., hero_title, contact_email"
                    value={newContent.content_key}
                    onChange={(e) => setNewContent(prev => ({ ...prev, content_key: e.target.value }))}
                    className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Content Type</label>
                  <select
                    value={newContent.content_type}
                    onChange={(e) => setNewContent(prev => ({ ...prev, content_type: e.target.value as any }))}
                    className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="hero">Hero Section</option>
                    <option value="marketing">Marketing Copy</option>
                    <option value="contact">Contact Information</option>
                    <option value="emergency">Emergency Information</option>
                    <option value="page_content">Page Content</option>
                    <option value="ui_text">UI Text</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Title</label>
                <input
                  type="text"
                  placeholder="Content title"
                  value={newContent.title}
                  onChange={(e) => setNewContent(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Content *</label>
                <textarea
                  placeholder="Enter the content"
                  value={newContent.content}
                  onChange={(e) => setNewContent(prev => ({ ...prev, content: e.target.value }))}
                  className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-32"
                  required
                />
              </div>

              <div className="flex gap-2">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-purple-500/20 hover:bg-purple-500/30"
                >
                  {isSubmitting ? 'Creating...' : 'Create Content'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetForm}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Content by Type */}
      <Tabs defaultValue="hero" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="hero">Hero</TabsTrigger>
          <TabsTrigger value="marketing">Marketing</TabsTrigger>
          <TabsTrigger value="contact">Contact</TabsTrigger>
          <TabsTrigger value="emergency">Emergency</TabsTrigger>
          <TabsTrigger value="page_content">Pages</TabsTrigger>
          <TabsTrigger value="ui_text">UI Text</TabsTrigger>
        </TabsList>

        {Object.entries(groupedContent).map(([type, items]) => (
          <TabsContent key={type} value={type} className="space-y-4">
            {items.length === 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>No {type} content found</CardTitle>
                  <CardDescription>
                    Create your first {type} content item
                  </CardDescription>
                </CardHeader>
              </Card>
            ) : (
              <div className="grid gap-4">
                {items.map((item) => {
                  const Icon = contentTypeIcons[item.content_type as keyof typeof contentTypeIcons] || Globe;
                  const isEditing = editingItem?.id === item.id;

                  return (
                    <Card key={item.id} className={!item.is_active ? 'opacity-60' : ''}>
                      <CardHeader className="flex flex-row items-start justify-between space-y-0">
                        <div className="space-y-1 flex-1">
                          <div className="flex items-center gap-2">
                            <Icon className="h-5 w-5" />
                            <CardTitle className="text-lg">{item.content_key}</CardTitle>
                            <Badge
                              variant="secondary"
                              className={cn(contentTypeColors[item.content_type as keyof typeof contentTypeColors])}
                            >
                              {item.content_type}
                            </Badge>
                            <Badge variant={item.is_active ? 'default' : 'secondary'}>
                              {item.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          {item.title && (
                            <CardDescription className="font-medium">
                              {item.title}
                            </CardDescription>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setEditingItem(isEditing ? null : item)}
                          >
                            {isEditing ? <X className="h-4 w-4" /> : <Pencil className="h-4 w-4" />}
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {isEditing ? (
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium mb-2">Title</label>
                              <input
                                type="text"
                                value={editingItem.title || ''}
                                onChange={(e) => setEditingItem(prev => prev ? { ...prev, title: e.target.value } : null)}
                                className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium mb-2">Content</label>
                              <textarea
                                value={editingItem.content}
                                onChange={(e) => setEditingItem(prev => prev ? { ...prev, content: e.target.value } : null)}
                                className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-32"
                              />
                            </div>
                            <div className="flex gap-2">
                              <Button
                                onClick={() => handleUpdate(editingItem)}
                                disabled={isSubmitting}
                                className="bg-green-500/20 hover:bg-green-500/30"
                              >
                                <Save className="mr-2 h-4 w-4" />
                                {isSubmitting ? 'Saving...' : 'Save'}
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => setEditingItem(null)}
                              >
                                Cancel
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => handleToggleActive(item)}
                                className={item.is_active ? 'text-red-400' : 'text-green-400'}
                              >
                                {item.is_active ? 'Deactivate' : 'Activate'}
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <p className="text-gray-300">{item.content}</p>
                            <div className="text-sm text-muted-foreground">
                              Version {item.version} • Updated {new Date(item.updated_at).toLocaleDateString()}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default AdminContentManagement;
