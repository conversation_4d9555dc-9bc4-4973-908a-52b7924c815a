import React from 'react';
import { Calendar, MapPin, Users, Clock, Heart, Share2, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { ActivityWithDetails } from '@/types/activities';
import { useUserInteractions } from '@/hooks/useUserInteractions';

interface ActivityDetailsModalProps {
  activity: ActivityWithDetails | null;
  isOpen: boolean;
  onClose: () => void;
}

export const ActivityDetailsModal: React.FC<ActivityDetailsModalProps> = ({
  activity,
  isOpen,
  onClose,
}) => {
  const {
    isJoined,
    isFavorited,
    joinLoading,
    favoriteLoading,
    toggleJoin,
    toggleFavorite,
    recordView,
  } = useUserInteractions(activity?.id || 0);

  React.useEffect(() => {
    if (isOpen && activity) {
      recordView();
    }
  }, [isOpen, activity, recordView]);

  if (!activity) return null;

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'meetup': return Users;
      case 'workshop': return User;
      case 'performance': return Calendar;
      case 'game': return Users;
      default: return Calendar;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'meetup': return 'from-blue-500/20 to-blue-600/20';
      case 'workshop': return 'from-green-500/20 to-green-600/20';
      case 'performance': return 'from-purple-500/20 to-purple-600/20';
      case 'game': return 'from-yellow-500/20 to-yellow-600/20';
      default: return 'from-gray-500/20 to-gray-600/20';
    }
  };

  const IconComponent = getActivityIcon(activity.type);
  const colorClass = getActivityColor(activity.type);

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
              <IconComponent className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{activity.title}</h2>
              <Badge variant="secondary" className="bg-white/10 text-white/80 text-xs">
                {activity.type}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleFavorite}
              disabled={favoriteLoading}
              className={`text-white/70 hover:text-white ${isFavorited ? 'text-red-400' : ''}`}
            >
              <Heart className={`w-5 h-5 ${isFavorited ? 'fill-current' : ''}`} />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              className="text-white/70 hover:text-white"
            >
              <Share2 className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-4">
            {/* Description */}
            <div>
              <h3 className="text-base font-semibold text-white mb-2">About This Activity</h3>
              <p className="text-white/80 leading-relaxed text-sm">
                {activity.description || 'No description available for this activity.'}
              </p>
            </div>

            {/* Details */}
            <div>
              <h3 className="text-base font-semibold text-white mb-2">Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {activity.start_date && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Calendar className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">Start Date</p>
                      <p className="text-xs">{new Date(activity.start_date).toLocaleString()}</p>
                    </div>
                  </div>
                )}

                {activity.end_date && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Clock className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">End Date</p>
                      <p className="text-xs">{new Date(activity.end_date).toLocaleString()}</p>
                    </div>
                  </div>
                )}

                {activity.location && (
                  <div className="flex items-center gap-2 text-white/70">
                    <MapPin className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">Location</p>
                      <p className="text-xs">{activity.location}</p>
                    </div>
                  </div>
                )}

                {activity.capacity && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Users className="w-4 h-4" />
                    <div>
                      <p className="font-medium text-sm">Capacity</p>
                      <p className="text-xs">{activity.capacity} participants</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Info */}
            {(activity.duration || activity.is_featured) && (
              <div>
                <h3 className="text-base font-semibold text-white mb-2">Additional Information</h3>
                <div className="space-y-1">
                  {activity.duration && (
                    <p className="text-white/70 text-sm">
                      <span className="font-medium">Duration:</span> {activity.duration} minutes
                    </p>
                  )}
                  {activity.is_featured && (
                    <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs">
                      Featured Activity
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex gap-3">
            <Button
              onClick={toggleJoin}
              disabled={joinLoading}
              className={`flex-1 ${
                isJoined
                  ? 'bg-green-600/20 text-green-400 border border-green-600/30 hover:bg-green-600/30'
                  : 'bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white'
              }`}
            >
              {joinLoading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <span>{isJoined ? 'Leave Activity' : 'Join Activity'}</span>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={onClose}
              className="border-white/20 text-white hover:bg-white/10"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};
