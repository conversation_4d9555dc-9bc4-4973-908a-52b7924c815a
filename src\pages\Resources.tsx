import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Lightbulb, BookOpen, HelpCircle, Star } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/lib/supabase';
import TipDetailsModal from '@/components/tips/TipDetailsModal';
import GuideDetailsModal from '@/components/guides/GuideDetailsModal';
import FAQDetailsModal from '@/components/faqs/FAQDetailsModal';

interface Tip {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface Guide {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  estimated_read_time?: number;
  created_at: string;
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

const Resources: React.FC = () => {
  const [tips, setTips] = useState<Tip[]>([]);
  const [guides, setGuides] = useState<Guide[]>([]);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('tips');

  // Modal states
  const [selectedTip, setSelectedTip] = useState<Tip | null>(null);
  const [selectedGuide, setSelectedGuide] = useState<Guide | null>(null);
  const [selectedFaq, setSelectedFaq] = useState<FAQ | null>(null);

  useEffect(() => {
    fetchContent();
  }, []);

  const fetchContent = async () => {
    try {
      setLoading(true);

      // Fetch published tips
      const { data: tipsData, error: tipsError } = await supabase
        .from('tips')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (tipsError) throw tipsError;

      // Fetch published guides
      const { data: guidesData, error: guidesError } = await supabase
        .from('guides')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (guidesError) throw guidesError;

      // Fetch published FAQs
      const { data: faqsData, error: faqsError } = await supabase
        .from('faqs')
        .select('*')
        .eq('status', 'published')
        .order('is_featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (faqsError) throw faqsError;

      setTips((tipsData || []).map(tip => ({
        ...tip,
        description: tip.description || undefined,
        category: tip.category || undefined,
        tags: tip.tags || undefined,
        is_featured: tip.is_featured || undefined,
        view_count: tip.view_count || undefined,
        helpful_count: tip.helpful_count || undefined,
        created_at: tip.created_at || new Date().toISOString()
      })));
      setGuides((guidesData || []).map(guide => ({
        ...guide,
        description: guide.description || undefined,
        category: guide.category || undefined,
        tags: guide.tags || undefined,
        is_featured: guide.is_featured || undefined,
        view_count: guide.view_count || undefined,
        helpful_count: guide.helpful_count || undefined,
        estimated_read_time: guide.estimated_read_time || undefined,
        created_at: guide.created_at || new Date().toISOString()
      })));
      setFaqs((faqsData || []).map(faq => ({
        ...faq,
        category: faq.category || undefined,
        tags: faq.tags || undefined,
        is_featured: faq.is_featured || undefined,
        view_count: faq.view_count || undefined,
        helpful_count: faq.helpful_count || undefined,
        created_at: faq.created_at || new Date().toISOString()
      })));
    } catch (error) {
      console.error('Error fetching content:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterContent = (items: any[], type: string) => {
    return items.filter(item => {
      const matchesSearch = searchQuery === '' || 
        item.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.question?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.answer?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags?.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || 
        selectedCategory === 'featured' && item.is_featured ||
        item.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  };

  const getCategories = (type: string) => {
    switch (type) {
      case 'tips':
        return ['SURVIVAL', 'SOCIAL', 'BUDGET', 'COMFORT', 'EXPERIENCE', 'SAFETY'];
      case 'guides':
        return ['SAFETY', 'PACKING', 'CAMPING', 'FOOD', 'TRANSPORT', 'PLANNING', 'SOCIAL'];
      case 'faqs':
        return ['GENERAL', 'ACCOUNT', 'ACTIVITIES', 'SAFETY', 'TECHNICAL', 'BILLING', 'PRIVACY'];
      default:
        return [];
    }
  };

  const renderTipCard = (tip: Tip) => (
    <Card 
      key={tip.id} 
      className="cursor-pointer hover:shadow-lg transition-shadow duration-300 bg-white/5 backdrop-blur-md border border-white/20"
      onClick={() => setSelectedTip(tip)}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-yellow-400" />
            <CardTitle className="text-lg text-white">{tip.title}</CardTitle>
          </div>
          {tip.is_featured && (
            <Badge className="bg-yellow-500/20 text-yellow-300">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>
        {tip.category && (
          <Badge variant="secondary" className="w-fit">
            {tip.category.replace('_', ' ')}
          </Badge>
        )}
      </CardHeader>
      <CardContent>
        {tip.description && (
          <p className="text-white/70 text-sm mb-3">{tip.description}</p>
        )}
        <div className="flex items-center justify-between text-sm text-white/60">
          <span>{tip.view_count || 0} views</span>
          <span>{tip.helpful_count || 0} helpful</span>
        </div>
      </CardContent>
    </Card>
  );

  const renderGuideCard = (guide: Guide) => (
    <Card 
      key={guide.id} 
      className="cursor-pointer hover:shadow-lg transition-shadow duration-300 bg-white/5 backdrop-blur-md border border-white/20"
      onClick={() => setSelectedGuide(guide)}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-blue-400" />
            <CardTitle className="text-lg text-white">{guide.title}</CardTitle>
          </div>
          {guide.is_featured && (
            <Badge className="bg-yellow-500/20 text-yellow-300">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {guide.category && (
            <Badge variant="secondary">
              {guide.category.replace('_', ' ')}
            </Badge>
          )}
          {guide.estimated_read_time && (
            <Badge variant="outline" className="text-white/70 border-white/20">
              {guide.estimated_read_time} min read
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {guide.description && (
          <p className="text-white/70 text-sm mb-3">{guide.description}</p>
        )}
        <div className="flex items-center justify-between text-sm text-white/60">
          <span>{guide.view_count || 0} views</span>
          <span>{guide.helpful_count || 0} helpful</span>
        </div>
      </CardContent>
    </Card>
  );

  const renderFaqCard = (faq: FAQ) => (
    <Card 
      key={faq.id} 
      className="cursor-pointer hover:shadow-lg transition-shadow duration-300 bg-white/5 backdrop-blur-md border border-white/20"
      onClick={() => setSelectedFaq(faq)}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <HelpCircle className="w-5 h-5 text-green-400" />
            <CardTitle className="text-lg text-white">{faq.question}</CardTitle>
          </div>
          {faq.is_featured && (
            <Badge className="bg-yellow-500/20 text-yellow-300">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>
        {faq.category && (
          <Badge variant="secondary" className="w-fit">
            {faq.category.replace('_', ' ')}
          </Badge>
        )}
      </CardHeader>
      <CardContent>
        <p className="text-white/70 text-sm mb-3 line-clamp-2">{faq.answer}</p>
        <div className="flex items-center justify-between text-sm text-white/60">
          <span>{faq.view_count || 0} views</span>
          <span>{faq.helpful_count || 0} helpful</span>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="text-white text-lg">Loading resources...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-4">Festival Resources</h1>
          <p className="text-white/70 text-lg">Tips, guides, and answers to help you make the most of your festival experience</p>
        </motion.div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5" />
            <Input
              placeholder="Search resources..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              onClick={() => setSelectedCategory('all')}
              className="text-white border-white/20"
            >
              All
            </Button>
            <Button
              variant={selectedCategory === 'featured' ? 'default' : 'outline'}
              onClick={() => setSelectedCategory('featured')}
              className="text-white border-white/20"
            >
              <Star className="w-4 h-4 mr-2" />
              Featured
            </Button>
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-white/10">
            <TabsTrigger value="tips" className="text-white">Tips ({tips.length})</TabsTrigger>
            <TabsTrigger value="guides" className="text-white">Guides ({guides.length})</TabsTrigger>
            <TabsTrigger value="faqs" className="text-white">FAQs ({faqs.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="tips" className="mt-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filterContent(tips, 'tips').map(renderTipCard)}
            </div>
            {filterContent(tips, 'tips').length === 0 && (
              <div className="text-center py-12 text-white/60">
                No tips found matching your criteria.
              </div>
            )}
          </TabsContent>

          <TabsContent value="guides" className="mt-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filterContent(guides, 'guides').map(renderGuideCard)}
            </div>
            {filterContent(guides, 'guides').length === 0 && (
              <div className="text-center py-12 text-white/60">
                No guides found matching your criteria.
              </div>
            )}
          </TabsContent>

          <TabsContent value="faqs" className="mt-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filterContent(faqs, 'faqs').map(renderFaqCard)}
            </div>
            {filterContent(faqs, 'faqs').length === 0 && (
              <div className="text-center py-12 text-white/60">
                No FAQs found matching your criteria.
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Modals */}
      <TipDetailsModal
        tip={selectedTip}
        isOpen={!!selectedTip}
        onClose={() => setSelectedTip(null)}
      />
      <GuideDetailsModal
        guide={selectedGuide}
        isOpen={!!selectedGuide}
        onClose={() => setSelectedGuide(null)}
      />
      <FAQDetailsModal
        faq={selectedFaq}
        isOpen={!!selectedFaq}
        onClose={() => setSelectedFaq(null)}
      />
    </div>
  );
};

export default Resources;
