import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { useProfile } from '../../hooks/useProfile';
import { isAdminRole } from '@/lib/utils/auth';
import type { Database } from '../../types/supabase';

type ExternalLink = Database['public']['Tables']['external_links']['Row'];

const AdminExternalLinks: React.FC = () => {
  const navigate = useNavigate();
  const { profile, isLoading: profileLoading, error: profileError } = useProfile();
  const loading = profileLoading; // Ensure loading is correctly defined
  const [links, setLinks] = useState<ExternalLink[]>([]);
  const [newLink, setNewLink] = useState<{
    category: string;
    url: string;
    title: string;
  }>({
    category: 'COMMUNITY',
    url: '',
    title: '',
  });

  // Redirect non-admin users
  useEffect(() => {
    if (!loading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [loading, profile, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      console.log('Creating external link with data:', {
        type: newLink.category,
        url: newLink.url,
        title: newLink.title,
        active: true,
      });

      const { error } = await supabase
        .from('external_links')
        .insert([{
          category: newLink.category, // Use 'category' field
          url: newLink.url,
          title: newLink.title,
          active: true,
          // Note: removed 'description' as it doesn't exist in database schema
        }]);

      if (error) throw error;

      console.log('External link created successfully');

      // Reset form
      setNewLink({ category: 'community', url: '', title: '' });

      // Refresh links
      fetchLinks();
    } catch (error) {
      console.error('Error creating link:', error);
    }
  };

  const fetchLinks = async () => {
    try {
      const { data, error } = await supabase
        .from('external_links')
        .select('*')
        .order('type', { ascending: true });

      if (error) throw error;
      setLinks(data || []);
    } catch (error) {
      console.error('Error fetching links:', error);
    }
  };

  const handleToggleActive = async (link: Database['public']['Tables']['external_links']['Row']) => {
    try {
      const { error } = await supabase
        .from('external_links')
        .update({
          active: !link.active
        })
        .eq('id', link.id);

      if (error) throw error;
      fetchLinks();
    } catch (error) {
      console.error('Error updating link:', error);
    }
  };

  useEffect(() => {
    fetchLinks();
  }, []);

  if (loading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Manage External Links</h1>

      {/* Create Link Form */}
      <div className="bg-white/5 backdrop-blur-lg rounded-lg p-6 border border-white/10 mb-8">
        <h2 className="text-xl font-semibold mb-4">Add New Link</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="link-category" className="block text-sm font-medium mb-2">Category</label>
            <select
              id="link-category"
              title="Select Link Category"
              aria-label="Select Link Category"
              value={newLink.category}
              onChange={(e) => setNewLink(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="COMMUNITY">Community</option>
              <option value="CHAT">Chat</option>
              <option value="SOCIAL">Social Media</option>
              <option value="DISCORD">Discord</option>
              <option value="TELEGRAM">Telegram</option>
              <option value="WHATSAPP">WhatsApp</option>
              <option value="FACEBOOK">Facebook</option>
              <option value="REDDIT">Reddit</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Title</label>
            <input
              type="text"
              value={newLink.title}
              onChange={(e) => setNewLink(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="e.g., Festival Family Community"
              required
            />
          </div>
          <div>
            <label htmlFor="link-url" className="block text-sm font-medium mb-2">URL</label>
            <input
              id="link-url"
              type="url"
              value={newLink.url}
              onChange={(e) => setNewLink(prev => ({ ...prev, url: e.target.value }))}
              className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="https://..."
              required
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-purple-500/20 hover:bg-purple-500/30 rounded-lg transition"
          >
            Add Link
          </button>
        </form>
      </div>

      {/* Links List */}
      <div className="bg-white/5 backdrop-blur-lg rounded-lg border border-white/10">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/10">
                <th className="px-6 py-3 text-left">Category</th>
                <th className="px-6 py-3 text-left">Title</th>
                <th className="px-6 py-3 text-left">URL</th>
                <th className="px-6 py-3 text-left">Status</th>
                <th className="px-6 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {links.map((link) => (
                <tr key={link.id} className="border-b border-white/10">
                  <td className="px-6 py-4 capitalize">{link.category || 'general'}</td>
                  <td className="px-6 py-4">{link.title}</td>
                  <td className="px-6 py-4">
                    <a 
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-purple-400 hover:text-purple-300"
                    >
                      {link.url}
                    </a>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-2 py-1 rounded-full text-sm ${
                      link.active ? 'bg-green-500/20 text-green-300' : 'bg-gray-500/20 text-gray-300'
                    }`}>
                      {link.active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <button
                      onClick={() => handleToggleActive(link)}
                      className="px-3 py-1 bg-purple-500/20 hover:bg-purple-500/30 rounded transition"
                    >
                      {link.active ? 'Deactivate' : 'Activate'}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdminExternalLinks;
