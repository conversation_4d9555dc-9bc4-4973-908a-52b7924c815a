import React from 'react';
import { motion } from 'framer-motion';
import type { Event } from '@/types';
import { formatEventDate } from '@/lib/utils/dates';
import { glassmorphismClasses, textClasses, buttonClasses, hoverClasses } from '@/lib/utils/styles';

// Extend Event type with optional properties
interface ExtendedEvent extends Event {
  featured?: boolean;
  price?: number;
  attendees?: string[];
}

interface EventCardProps {
  event: ExtendedEvent;
  className?: string;
  onClick?: (event: ExtendedEvent) => void;
}

const EventCard: React.FC<EventCardProps> = ({ event, className, onClick }) => {
  // Fallback values for optional properties
  const featured = event.featured ?? false;
  const price = event.price ?? null;
  const attendees = event.attendees ?? [];

  const handleClick = () => {
    if (onClick) onClick(event);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02 }}
      className={`${glassmorphismClasses.card} ${hoverClasses.glow} rounded-xl overflow-hidden ${className}`}
      onClick={handleClick}
    >
      <div className="aspect-video relative">
        <img
          src={event.image_url ?? '/images/default-event.jpg'}
          alt={event.title}
          className="w-full h-full object-cover"
        />
        {featured && (
          <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs">
            Featured
          </div>
        )}
        <div className={`absolute bottom-0 left-0 right-0 ${glassmorphismClasses.card} backdrop-blur-md p-4`}>
          <h3 className={`${textClasses.heading} text-white`}>{event.title}</h3>
          <p className={`${textClasses.small} text-white/80 mt-1`}>
            {formatEventDate(event.start_date, event.end_date)}
          </p>
        </div>
      </div>

      <div className="p-4">
        <p className={`${textClasses.body} line-clamp-2`}>{event.description}</p>

        <div className="mt-4 space-y-3">
          {/* Location and Capacity */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-400">📍 {event.location}</span>
            {event.capacity && (
              <span className="text-gray-400">
                Capacity: {event.capacity} spots
              </span>
            )}
          </div>

          {/* Price Information */}
          {price !== null && (
            <div className="flex items-center justify-between">
              <span className="font-semibold text-green-500">
                ${price.toFixed(2)}
              </span>
            </div>
          )}

          {/* Attendees */}
          {attendees.length > 0 && (
            <div className="flex items-center">
              <div className="flex -space-x-2">
                {attendees.slice(0, 3).map((attendeeId) => (
                  <div
                    key={attendeeId}
                    className="w-6 h-6 rounded-full bg-gray-300 border-2 border-white"
                  />
                ))}
              </div>
              {attendees.length > 3 && (
                <span className="ml-2 text-xs text-gray-500">
                  +{attendees.length - 3} more
                </span>
              )}
            </div>
          )}

          {/* Action Button */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`${buttonClasses.primary} ${hoverClasses.glow} w-full mt-4`}
            onClick={(e) => {
              e.stopPropagation();
              onClick?.(event);
            }}
          >
            View Details
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default EventCard;
