import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabase';
import AnnouncementPopup from '../components/announcements/AnnouncementPopup';
import { useAnnouncementDismissal } from '@/hooks/useAnnouncementDismissal';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, MapPin, Users, Clock, Star, Bell, Activity, TrendingUp, Heart, Eye } from 'lucide-react';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import type { Announcement } from '@/types';
import type { Database } from '@/types/supabase';
import { AnnouncementDisplayType, AnnouncementPriority } from '@/types/announcements';

type AnnouncementRow = Database['public']['Tables']['announcements']['Row'] & {
  category?: Array<{
    id: string;
    name: string;
    color: string | null;
    description: string | null;
    created_at: string;
    updated_at: string;
  }>;
};

interface Festival {
  id: string;
  name: string;
  description: string | null;
  start_date?: string;
  end_date?: string;
  location?: string;
  featured?: boolean;
}

interface Activity {
  id: string;
  title: string;
  description: string;
  type: string;
  location?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  capacity?: number | null;
  is_featured?: boolean | null;
  festival_id?: string | null;
}

interface Tip {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  helpful_count?: number | null;
  view_count?: number | null;
}

interface Guide {
  id: string;
  title: string;
  description?: string | null;
  category: string | null;
  is_featured?: boolean | null;
  view_count?: number | null;
}

const Home: React.FC = () => {
  const { user } = useAuth();
  const [showAnnouncement, setShowAnnouncement] = useState(false);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  // New state for dashboard data
  const [todaysActivities, setTodaysActivities] = useState<Activity[]>([]);
  const [featuredTips, setFeaturedTips] = useState<Tip[]>([]);
  const [featuredGuides, setFeaturedGuides] = useState<Guide[]>([]);
  const [recentAnnouncements, setRecentAnnouncements] = useState<AnnouncementRow[]>([]);
  const [loading, setLoading] = useState(true);

  // Announcement dismissal
  const {
    dismissAnnouncement,
    isDismissed
  } = useAnnouncementDismissal();

  // Get today's date for filtering
  const today = new Date().toISOString().split('T')[0];

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch today's activities
      const { data: activities } = await supabase
        .from('activities')
        .select('*')
        .gte('start_date', today)
        .lte('start_date', `${today}T23:59:59`)
        .order('start_date', { ascending: true })
        .limit(6);

      // Fetch featured tips
      const { data: tips } = await supabase
        .from('tips')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('helpful_count', { ascending: false })
        .limit(4);

      // Fetch featured guides
      const { data: guides } = await supabase
        .from('guides')
        .select('*')
        .eq('is_featured', true)
        .eq('status', 'published')
        .order('view_count', { ascending: false })
        .limit(4);

      // Fetch recent announcements
      const { data: announcementsData } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(3);

      setTodaysActivities(activities || []);
      setFeaturedTips(tips || []);
      setFeaturedGuides(guides || []);
      setRecentAnnouncements(announcementsData || []);
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch popup announcements
  const fetchAnnouncements = async () => {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .eq('display_type', 'popup')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      const mappedAnnouncements: Announcement[] = (data || []).map(row => ({
        id: parseInt(row.id),
        title: row.title,
        content: row.content,
        priority: row.priority as AnnouncementPriority || 'NORMAL',
        target_audience: row.target_audience as string[] || [],
        is_active: row.active || false,
        is_featured: row.is_featured || false,
        display_type: row.display_type as AnnouncementDisplayType,
        created_at: row.created_at || '',
        updated_at: row.updated_at,
      }));

      const activeAnnouncements = mappedAnnouncements.filter(ann => 
        ann.is_active && !isDismissed(ann.id.toString())
      );

      setAnnouncements(activeAnnouncements);
      
      if (activeAnnouncements.length > 0) {
        setShowAnnouncement(true);
      }
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    fetchAnnouncements();
  }, []);

  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading your festival dashboard...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900">
      {/* Popup Announcement */}
      {showAnnouncement && announcements.length > 0 && (
        <AnnouncementPopup
          announcement={{
            ...announcements[currentIndex],
            id: announcements[currentIndex].id.toString(),
            priority: 'NORMAL' as AnnouncementPriority,
            created_by: '',
            is_active: true,
            updated_at: announcements[currentIndex].updated_at ?? new Date().toISOString(),
          }}
          isOpen={showAnnouncement}
          onClose={() => setShowAnnouncement(false)}
          onDismiss={() => dismissAnnouncement(announcements[currentIndex].id.toString())}
        />
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
            Welcome back, {user?.profile?.full_name || user?.user_metadata?.full_name || 'Festival Family'}! 🎪
          </h1>
          <p className="text-white/80 text-lg">Here's what's happening in your festival world</p>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8"
        >
          <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
            <CardContent className="p-4 text-center">
              <Activity className="w-8 h-8 mx-auto mb-2 text-yellow-400" />
              <div className="text-2xl font-bold">{todaysActivities.length}</div>
              <div className="text-sm text-white/80">Today's Activities</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
            <CardContent className="p-4 text-center">
              <TrendingUp className="w-8 h-8 mx-auto mb-2 text-green-400" />
              <div className="text-2xl font-bold">{featuredTips.length}</div>
              <div className="text-sm text-white/80">Featured Tips</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
            <CardContent className="p-4 text-center">
              <Bell className="w-8 h-8 mx-auto mb-2 text-blue-400" />
              <div className="text-2xl font-bold">{recentAnnouncements.length}</div>
              <div className="text-sm text-white/80">New Updates</div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Today's Activities */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white h-fit">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-yellow-400" />
                  Happening Today
                </CardTitle>
              </CardHeader>
              <CardContent>
                {todaysActivities.length > 0 ? (
                  <div className="space-y-3">
                    {todaysActivities.map((activity) => (
                      <div key={activity.id} className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-sm">{activity.title}</h3>
                          <Badge variant="outline" className="text-xs border-yellow-400 text-yellow-400">
                            {activity.type}
                          </Badge>
                        </div>
                        <p className="text-white/70 text-xs mb-2 line-clamp-2">{activity.description}</p>
                        <div className="flex items-center gap-3 text-xs text-white/60">
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatTime(activity.start_date)}
                          </div>
                          {activity.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {activity.location}
                            </div>
                          )}
                          {activity.capacity && (
                            <div className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              {activity.capacity} spots
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    <Link to="/activities">
                      <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10">
                        View All Activities
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 mx-auto mb-3 text-white/40" />
                    <p className="text-white/60 mb-4">No activities scheduled for today</p>
                    <Link to="/activities">
                      <Button variant="outline" className="text-white border-white/20 hover:bg-white/10">
                        Browse Activities
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Updates */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white h-fit">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="w-5 h-5 text-blue-400" />
                  Latest Updates
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentAnnouncements.length > 0 ? (
                  <div className="space-y-3">
                    {recentAnnouncements.map((announcement) => (
                      <div key={announcement.id} className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-sm">{announcement.title}</h3>
                          {announcement.priority && (
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                announcement.priority === 'high' 
                                  ? 'border-red-400 text-red-400' 
                                  : announcement.priority === 'medium'
                                  ? 'border-yellow-400 text-yellow-400'
                                  : 'border-blue-400 text-blue-400'
                              }`}
                            >
                              {announcement.priority}
                            </Badge>
                          )}
                        </div>
                        <p className="text-white/70 text-xs line-clamp-2">{announcement.content}</p>
                        <div className="text-xs text-white/50 mt-2">
                          {formatDate(announcement.created_at)}
                        </div>
                      </div>
                    ))}
                    <Link to="/famhub">
                      <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10">
                        View All Updates
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Bell className="w-12 h-12 mx-auto mb-3 text-white/40" />
                    <p className="text-white/60">No recent updates</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Featured Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8"
        >
          <h2 className="text-2xl font-bold text-white mb-6 text-center">Featured Festival Resources</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Featured Tips */}
            <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-yellow-400" />
                  Top Tips
                </CardTitle>
              </CardHeader>
              <CardContent>
                {featuredTips.length > 0 ? (
                  <div className="space-y-3">
                    {featuredTips.slice(0, 3).map((tip) => (
                      <div key={tip.id} className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <h3 className="font-semibold text-sm mb-1">{tip.title}</h3>
                        <p className="text-white/70 text-xs mb-2 line-clamp-2">{tip.description}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs border-green-400 text-green-400">
                            {tip.category}
                          </Badge>
                          <div className="flex items-center gap-2 text-xs text-white/60">
                            <div className="flex items-center gap-1">
                              <Heart className="w-3 h-3" />
                              {tip.helpful_count || 0}
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="w-3 h-3" />
                              {tip.view_count || 0}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    <Link to="/resources">
                      <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10">
                        View All Tips
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Star className="w-12 h-12 mx-auto mb-3 text-white/40" />
                    <p className="text-white/60">No featured tips available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Featured Guides */}
            <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-purple-400" />
                  Popular Guides
                </CardTitle>
              </CardHeader>
              <CardContent>
                {featuredGuides.length > 0 ? (
                  <div className="space-y-3">
                    {featuredGuides.slice(0, 3).map((guide) => (
                      <div key={guide.id} className="bg-white/5 rounded-lg p-3 border border-white/10">
                        <h3 className="font-semibold text-sm mb-1">{guide.title}</h3>
                        <p className="text-white/70 text-xs mb-2 line-clamp-2">{guide.description}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs border-purple-400 text-purple-400">
                            {guide.category}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-white/60">
                            <Eye className="w-3 h-3" />
                            {guide.view_count || 0} views
                          </div>
                        </div>
                      </div>
                    ))}
                    <Link to="/resources">
                      <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10">
                        View All Guides
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Star className="w-12 h-12 mx-auto mb-3 text-white/40" />
                    <p className="text-white/60">No featured guides available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8 text-center"
        >
          <h2 className="text-2xl font-bold text-white mb-6">Quick Actions</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link to="/discover">
              <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10 p-4">
                <div className="flex flex-col items-center gap-2">
                  <Calendar className="w-6 h-6" />
                  <span className="text-sm">Discover Events</span>
                </div>
              </Button>
            </Link>
            <Link to="/activities">
              <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10 p-4">
                <div className="flex flex-col items-center gap-2">
                  <Activity className="w-6 h-6" />
                  <span className="text-sm">Join Activities</span>
                </div>
              </Button>
            </Link>
            <Link to="/famhub">
              <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10 p-4">
                <div className="flex flex-col items-center gap-2">
                  <Users className="w-6 h-6" />
                  <span className="text-sm">FamHub</span>
                </div>
              </Button>
            </Link>
            <Link to="/resources">
              <Button variant="outline" className="w-full text-white border-white/20 hover:bg-white/10 p-4">
                <div className="flex flex-col items-center gap-2">
                  <Star className="w-6 h-6" />
                  <span className="text-sm">Resources</span>
                </div>
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Home;
