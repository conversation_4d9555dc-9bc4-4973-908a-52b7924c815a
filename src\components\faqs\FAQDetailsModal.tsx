import React from 'react';
import { Help<PERSON>ir<PERSON>, Tag, Heart, Share2, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface FAQDetailsModalProps {
  faq: FAQ | null;
  isOpen: boolean;
  onClose: () => void;
}

export const FAQDetailsModal: React.FC<FAQDetailsModalProps> = ({
  faq,
  isOpen,
  onClose,
}) => {
  if (!faq) return null;

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'GENERAL': return 'from-blue-500 to-cyan-600';
      case 'ACCOUNT': return 'from-purple-500 to-pink-600';
      case 'ACTIVITIES': return 'from-green-500 to-emerald-600';
      case 'SAFETY': return 'from-red-500 to-rose-600';
      case 'TECHNICAL': return 'from-indigo-500 to-purple-600';
      case 'BILLING': return 'from-yellow-500 to-orange-600';
      case 'PRIVACY': return 'from-gray-500 to-slate-600';
      default: return 'from-gray-500 to-slate-600';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'GENERAL': return 'General Questions';
      case 'ACCOUNT': return 'Account & Profile';
      case 'ACTIVITIES': return 'Activities & Events';
      case 'SAFETY': return 'Safety & Security';
      case 'TECHNICAL': return 'Technical Support';
      case 'BILLING': return 'Billing & Payments';
      case 'PRIVACY': return 'Privacy & Data';
      default: return category;
    }
  };

  const colorClass = getCategoryColor(faq.category || '');

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      showCloseButton={false}
      className="bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center gap-3 p-6 border-b border-white/10">
          <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
            <HelpCircle className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-white">FAQ</h2>
            {faq.category && (
              <Badge variant="secondary" className="mt-1">
                {getCategoryLabel(faq.category)}
              </Badge>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
                {/* Question */}
                <div className="bg-white/5 rounded-lg p-4 border-l-4 border-blue-400">
                  <h3 className="text-lg font-semibold text-white mb-2">Question</h3>
                  <p className="text-white/90">{faq.question}</p>
                </div>

                {/* Answer */}
                <div className="bg-white/5 rounded-lg p-4 border-l-4 border-green-400">
                  <h3 className="text-lg font-semibold text-white mb-2">Answer</h3>
                  <div className="prose prose-invert max-w-none">
                    <p className="text-white/90 leading-relaxed whitespace-pre-wrap">{faq.answer}</p>
                  </div>
                </div>

                {/* Tags */}
                {faq.tags && faq.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {faq.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-white/70 border-white/20">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Stats */}
                <div className="flex items-center gap-4 text-sm text-white/60">
                  {faq.view_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <HelpCircle className="w-4 h-4" />
                      {faq.view_count} views
                    </span>
                  )}
                  {faq.helpful_count !== undefined && (
                    <span className="flex items-center gap-1">
                      <Heart className="w-4 h-4" />
                      {faq.helpful_count} helpful
                    </span>
                  )}
                  <span>
                    Updated {new Date(faq.created_at).toLocaleDateString()}
                  </span>
                </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="text-white border-white/20 hover:bg-white/10"
              >
                <Heart className="w-4 h-4 mr-2" />
                Helpful
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-white border-white/20 hover:bg-white/10"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Close
              </Button>
            </div>
            {faq.is_featured && (
              <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                <Star className="w-3 h-3 mr-1" />
                Featured FAQ
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default FAQDetailsModal;
