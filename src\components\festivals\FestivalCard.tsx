/**
 * FestivalCard Component
 * 
 * A reusable card component for displaying festival information.
 * It follows the component composition pattern and uses UI components.
 */

import React from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Calendar, MapPin, Users, Clock } from 'lucide-react'
import { format } from 'date-fns'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import type { Festival } from '@/types/supabase'

export interface FestivalCardProps {
  festival: Festival
  isFeatured?: boolean
  showAttendees?: boolean
  className?: string
}

export function FestivalCard({
  festival,
  isFeatured = false,
  showAttendees = false,
  className = '',
}: FestivalCardProps) {
  // Format dates with null safety
  const startDate = festival.start_date ? new Date(festival.start_date) : null
  const endDate = festival.end_date ? new Date(festival.end_date) : null

  // Validate parsed dates
  const validStartDate = startDate && !isNaN(startDate.getTime()) ? startDate : null
  const validEndDate = endDate && !isNaN(endDate.getTime()) ? endDate : null
  
  // Format date range with validated dates
  const dateRange = validStartDate && validEndDate
    ? `${format(validStartDate, 'MMM d')} - ${format(validEndDate, 'MMM d, yyyy')}`
    : 'Dates TBA'

  // Calculate days until festival with null safety
  const daysUntil = validStartDate
    ? Math.max(0, Math.ceil((validStartDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))
    : null
  
  // Mock attendees (in a real app, this would come from props or a database query)
  const attendees = [
    { id: '1', name: 'Alex', avatar: '/avatars/alex.jpg' },
    { id: '2', name: 'Sam', avatar: '/avatars/sam.jpg' },
    { id: '3', name: 'Jordan', avatar: '/avatars/jordan.jpg' },
  ]
  
  return (
    <motion.div
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className={className}
    >
      <Card className="h-full overflow-hidden bg-white/5 backdrop-blur-md border-white/10 hover:border-electric-violet/30 transition-colors">
        {/* Festival Image */}
        <div className="relative h-48 w-full">
          {festival.image_url ? (
            <img
              src={festival.image_url}
              alt={festival.name}
              className="h-full w-full object-cover"
            />
          ) : (
            <div className="h-full w-full bg-gradient-to-br from-electric-violet/30 to-midnight-purple/50 flex items-center justify-center">
              <span className="text-white/50">No image available</span>
            </div>
          )}
          
          {/* Featured Badge */}
          {isFeatured && (
            <Badge className="absolute top-2 right-2 bg-electric-violet">
              Featured
            </Badge>
          )}
          
          {/* Festival Name Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            <h3 className="text-xl font-bold text-white">{festival.name}</h3>
            <p className="text-sm text-white/80">{dateRange}</p>
          </div>
        </div>
        
        {/* Festival Details */}
        <CardContent className="p-4">
          {/* Location */}
          {festival.location && (
            <div className="flex items-center text-white/70 mb-2">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{festival.location}</span>
            </div>
          )}
          
          {/* Days Until */}
          {daysUntil !== null && (
            <div className="flex items-center text-white/70 mb-2">
              <Clock className="w-4 h-4 mr-2" />
              <span>{daysUntil === 0 ? 'Today!' : `${daysUntil} days away`}</span>
            </div>
          )}
          
          {/* Description */}
          {festival.description && (
            <p className="text-white/80 line-clamp-2 mt-2">{festival.description}</p>
          )}
          
          {/* Attendees */}
          {showAttendees && (
            <div className="mt-4">
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-2 text-white/70" />
                <span className="text-sm text-white/70">Attendees</span>
              </div>
              <div className="flex -space-x-2 mt-2">
                {attendees.map((attendee) => (
                  <Avatar key={attendee.id} className="border-2 border-midnight-purple w-8 h-8">
                    <AvatarImage src={attendee.avatar} alt={attendee.name} />
                    <AvatarFallback>{attendee.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                ))}
                <div className="w-8 h-8 rounded-full bg-electric-violet/30 flex items-center justify-center text-xs text-white border-2 border-midnight-purple">
                  +42
                </div>
              </div>
            </div>
          )}
        </CardContent>
        
        {/* Actions */}
        <CardFooter className="p-4 pt-0 flex justify-between">
          <Button
            variant="outline"
            size="sm"
            className="border-white/10 text-white/80 hover:text-white hover:bg-white/5"
          >
            Details
          </Button>
          
          <Link to={`/festivals/${festival.id}`}>
            <Button size="sm">View Festival</Button>
          </Link>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
