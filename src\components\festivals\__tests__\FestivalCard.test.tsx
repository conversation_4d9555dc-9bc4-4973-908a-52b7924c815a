/**
 * FestivalCard Component Tests
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { FestivalCard } from '../FestivalCard'
import { renderWithProviders } from '@/test/utils'

// Mock festival data
const mockFestival = {
  id: '1', // Changed to string UUID
  name: 'Test Festival',
  description: 'This is a test festival',
  start_date: '2023-07-15',
  end_date: '2023-07-17',
  location: 'Test Location',
  image_url: 'https://example.com/image.jpg',
  created_at: '2023-01-01',
  updated_at: null,
  website: null,
  status: 'PUBLISHED',
  created_by: null,
  featured: false,
}

describe('FestivalCard', () => {
  test('renders festival information correctly', () => {
    renderWithProviders(<FestivalCard festival={mockFestival} />)
    
    // Check if festival name is rendered
    expect(screen.getByText('Test Festival')).toBeInTheDocument()
    
    // Check if festival location is rendered
    expect(screen.getByText('Test Location')).toBeInTheDocument()
    
    // Check if festival description is rendered
    expect(screen.getByText('This is a test festival')).toBeInTheDocument()
    
    // Check if date range is rendered (format may vary)
    expect(screen.getByText(/Jul 15 - Jul 17, 2023/)).toBeInTheDocument()
  })
  
  test('renders featured badge when isFeatured is true', () => {
    renderWithProviders(<FestivalCard festival={mockFestival} isFeatured={true} />)
    
    expect(screen.getByText('Featured')).toBeInTheDocument()
  })
  
  test('does not render featured badge when isFeatured is false', () => {
    renderWithProviders(<FestivalCard festival={mockFestival} isFeatured={false} />)
    
    expect(screen.queryByText('Featured')).not.toBeInTheDocument()
  })
  
  test('renders attendees when showAttendees is true', () => {
    renderWithProviders(<FestivalCard festival={mockFestival} showAttendees={true} />)
    
    expect(screen.getByText('Attendees')).toBeInTheDocument()
  })
  
  test('does not render attendees when showAttendees is false', () => {
    renderWithProviders(<FestivalCard festival={mockFestival} showAttendees={false} />)
    
    expect(screen.queryByText('Attendees')).not.toBeInTheDocument()
  })
  
  test('renders buttons correctly', () => {
    renderWithProviders(<FestivalCard festival={mockFestival} />)
    
    expect(screen.getByText('Details')).toBeInTheDocument()
    expect(screen.getByText('View Festival')).toBeInTheDocument()
  })
  
  test('renders placeholder when image_url is not provided', () => {
    const festivalWithoutImage = { ...mockFestival, image_url: null }
    renderWithProviders(<FestivalCard festival={festivalWithoutImage} />)
    
    expect(screen.getByText('No image available')).toBeInTheDocument()
  })
})
