import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { useProfile } from '../../hooks/useProfile';
import { isAdminRole } from '@/lib/utils/auth';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Phone, AlertTriangle, Shield, Plus, Pencil, Trash, Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EmergencyContact {
  id: string;
  festival_id: string | null;
  contact_type: string;
  name: string;
  phone: string | null;
  email: string | null;
  description: string | null;
  is_primary: boolean | null;
  order_index: number | null;
  is_active: boolean | null;
  created_by: string | null;
  created_at: string | null;
  updated_at: string | null;
}

interface SafetyInformation {
  id: string;
  festival_id: string | null;
  safety_category: string;
  title: string;
  content: string;
  priority: string | null;
  is_alert: boolean | null;
  order_index: number | null;
  is_active: boolean | null;
  created_by: string | null;
  created_at: string | null;
  updated_at: string | null;
}

interface Festival {
  id: string;
  name: string;
}

const contactTypeColors = {
  festival_organizer: 'bg-blue-500/20 text-blue-400',
  medical: 'bg-red-500/20 text-red-400',
  security: 'bg-yellow-500/20 text-yellow-400',
  local_emergency: 'bg-red-600/20 text-red-300',
  festival_family: 'bg-purple-500/20 text-purple-400'
};

const safetyPriorityColors = {
  low: 'bg-green-500/20 text-green-400',
  medium: 'bg-yellow-500/20 text-yellow-400',
  high: 'bg-orange-500/20 text-orange-400',
  critical: 'bg-red-500/20 text-red-400'
};

const AdminEmergencyManagement: React.FC = () => {
  const navigate = useNavigate();
  const { profile, loading: profileLoading } = useProfile();
  const [emergencyContacts, setEmergencyContacts] = useState<EmergencyContact[]>([]);
  const [safetyInfo, setSafetyInfo] = useState<SafetyInformation[]>([]);
  const [festivals, setFestivals] = useState<Festival[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFestival, setSelectedFestival] = useState<string>('');
  const [showContactForm, setShowContactForm] = useState(false);
  const [showSafetyForm, setShowSafetyForm] = useState(false);
  const [editingContact, setEditingContact] = useState<EmergencyContact | null>(null);
  const [editingSafety, setEditingSafety] = useState<SafetyInformation | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  type ContactType = 'festival_organizer' | 'medical' | 'security' | 'local_emergency' | 'festival_family';
  type SafetyCategory = 'general' | 'medical' | 'security' | 'weather' | 'transportation' | 'emergency_procedures';
  type Priority = 'low' | 'medium' | 'high' | 'critical';

  const [newContact, setNewContact] = useState({
    contact_type: 'festival_organizer' as ContactType,
    name: '',
    phone: '',
    email: '',
    description: '',
    is_primary: false,
    order_index: 0
  });

  const [newSafety, setNewSafety] = useState({
    safety_category: 'general' as SafetyCategory,
    title: '',
    content: '',
    priority: 'medium' as Priority,
    is_alert: false,
    order_index: 0
  });

  // Redirect non-admin users
  useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [profileLoading, profile, navigate]);

  // Fetch festivals
  const fetchFestivals = async () => {
    try {
      const { data, error } = await supabase
        .from('festivals')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setFestivals(data || []);
      
      // Auto-select first festival if available
      if (data && data.length > 0 && !selectedFestival) {
        setSelectedFestival(data[0].id);
      }
    } catch (error) {
      console.error('Error fetching festivals:', error);
      toast.error('Failed to load festivals');
    }
  };

  // Fetch emergency contacts
  const fetchEmergencyContacts = async () => {
    if (!selectedFestival) return;

    try {
      const { data, error } = await supabase
        .from('emergency_contacts')
        .select('*')
        .eq('festival_id', selectedFestival)
        .order('is_primary', { ascending: false })
        .order('order_index')
        .order('contact_type');

      if (error) throw error;
      setEmergencyContacts(data || []);
    } catch (error) {
      console.error('Error fetching emergency contacts:', error);
      toast.error('Failed to load emergency contacts');
    }
  };

  // Fetch safety information
  const fetchSafetyInfo = async () => {
    if (!selectedFestival) return;

    try {
      const { data, error } = await supabase
        .from('safety_information')
        .select('*')
        .eq('festival_id', selectedFestival)
        .order('priority', { ascending: false })
        .order('order_index')
        .order('safety_category');

      if (error) throw error;
      setSafetyInfo(data || []);
    } catch (error) {
      console.error('Error fetching safety information:', error);
      toast.error('Failed to load safety information');
    }
  };

  useEffect(() => {
    if (!profileLoading && isAdminRole(profile?.role)) {
      fetchFestivals();
    }
  }, [profileLoading, profile]);

  useEffect(() => {
    if (selectedFestival) {
      setLoading(true);
      Promise.all([
        fetchEmergencyContacts(),
        fetchSafetyInfo()
      ]).finally(() => setLoading(false));
    }
  }, [selectedFestival]);

  const handleCreateContact = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFestival) return;

    setIsSubmitting(true);

    try {
      const contactData = {
        festival_id: selectedFestival,
        contact_type: newContact.contact_type,
        name: newContact.name,
        phone: newContact.phone,
        email: newContact.email,
        description: newContact.description,
        is_primary: newContact.is_primary,
        order_index: newContact.order_index,
        is_active: true,
        created_by: profile?.id
      };

      const { error } = await supabase
        .from('emergency_contacts')
        .insert([contactData]);

      if (error) throw error;

      toast.success('Emergency contact created successfully!');
      resetContactForm();
      fetchEmergencyContacts();
    } catch (error) {
      console.error('Error creating emergency contact:', error);
      toast.error('Failed to create emergency contact');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCreateSafety = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFestival) return;

    setIsSubmitting(true);

    try {
      const safetyData = {
        festival_id: selectedFestival,
        safety_category: newSafety.safety_category,
        title: newSafety.title,
        content: newSafety.content,
        priority: newSafety.priority,
        is_alert: newSafety.is_alert,
        order_index: newSafety.order_index,
        is_active: true,
        created_by: profile?.id
      };

      const { error } = await supabase
        .from('safety_information')
        .insert([safetyData]);

      if (error) throw error;

      toast.success('Safety information created successfully!');
      resetSafetyForm();
      fetchSafetyInfo();
    } catch (error) {
      console.error('Error creating safety information:', error);
      toast.error('Failed to create safety information');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteContact = async (contactId: string) => {
    if (!confirm('Are you sure you want to delete this emergency contact?')) return;

    try {
      const { error } = await supabase
        .from('emergency_contacts')
        .delete()
        .eq('id', contactId);

      if (error) throw error;

      toast.success('Emergency contact deleted successfully!');
      fetchEmergencyContacts();
    } catch (error) {
      console.error('Error deleting emergency contact:', error);
      toast.error('Failed to delete emergency contact');
    }
  };

  const handleDeleteSafety = async (safetyId: string) => {
    if (!confirm('Are you sure you want to delete this safety information?')) return;

    try {
      const { error } = await supabase
        .from('safety_information')
        .delete()
        .eq('id', safetyId);

      if (error) throw error;

      toast.success('Safety information deleted successfully!');
      fetchSafetyInfo();
    } catch (error) {
      console.error('Error deleting safety information:', error);
      toast.error('Failed to delete safety information');
    }
  };

  const resetContactForm = () => {
    setShowContactForm(false);
    setEditingContact(null);
    setNewContact({
      contact_type: 'festival_organizer' as ContactType,
      name: '',
      phone: '',
      email: '',
      description: '',
      is_primary: false,
      order_index: 0
    });
  };

  const resetSafetyForm = () => {
    setShowSafetyForm(false);
    setEditingSafety(null);
    setNewSafety({
      safety_category: 'general' as SafetyCategory,
      title: '',
      content: '',
      priority: 'medium' as Priority,
      is_alert: false,
      order_index: 0
    });
  };

  if (profileLoading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Emergency & Safety Management</h1>
          <p className="text-muted-foreground mt-2">
            Manage emergency contacts and safety information for festivals
          </p>
        </div>
        
        {/* Festival Selector */}
        <div className="flex items-center gap-4">
          <select
            value={selectedFestival}
            onChange={(e) => setSelectedFestival(e.target.value)}
            className="px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="">Select Festival</option>
            {festivals.map(festival => (
              <option key={festival.id} value={festival.id}>
                {festival.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {!selectedFestival ? (
        <Card>
          <CardHeader>
            <CardTitle>Select a Festival</CardTitle>
            <CardDescription>
              Choose a festival to manage its emergency contacts and safety information
            </CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <Tabs defaultValue="contacts" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="contacts">Emergency Contacts</TabsTrigger>
            <TabsTrigger value="safety">Safety Information</TabsTrigger>
          </TabsList>

          <TabsContent value="contacts" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Emergency Contacts</h2>
              <Button
                onClick={() => setShowContactForm(true)}
                className="bg-primary/20 hover:bg-primary/30"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Contact
              </Button>
            </div>

            {/* Create Contact Form */}
            {showContactForm && (
              <Card>
                <CardHeader>
                  <CardTitle>Add Emergency Contact</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCreateContact} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Contact Type</label>
                        <select
                          value={newContact.contact_type}
                          onChange={(e) => setNewContact(prev => ({ ...prev, contact_type: e.target.value as ContactType }))}
                          className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          <option value="festival_organizer">Festival Organizer</option>
                          <option value="medical">Medical</option>
                          <option value="security">Security</option>
                          <option value="local_emergency">Local Emergency</option>
                          <option value="festival_family">Festival Family</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Name *</label>
                        <input
                          type="text"
                          placeholder="Contact name"
                          value={newContact.name}
                          onChange={(e) => setNewContact(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Phone</label>
                        <input
                          type="tel"
                          placeholder="****** 567 8900"
                          value={newContact.phone}
                          onChange={(e) => setNewContact(prev => ({ ...prev, phone: e.target.value }))}
                          className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Email</label>
                        <input
                          type="email"
                          placeholder="<EMAIL>"
                          value={newContact.email}
                          onChange={(e) => setNewContact(prev => ({ ...prev, email: e.target.value }))}
                          className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Description</label>
                      <textarea
                        placeholder="Additional information about this contact"
                        value={newContact.description}
                        onChange={(e) => setNewContact(prev => ({ ...prev, description: e.target.value }))}
                        className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-24"
                      />
                    </div>

                    <div className="flex items-center gap-4">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={newContact.is_primary}
                          onChange={(e) => setNewContact(prev => ({ ...prev, is_primary: e.target.checked }))}
                          className="rounded border-white/10 bg-white/5 text-purple-500 focus:ring-purple-500"
                        />
                        <span className="text-sm">Primary contact</span>
                      </label>
                      <div>
                        <label className="block text-sm font-medium mb-1">Order</label>
                        <input
                          type="number"
                          min="0"
                          value={newContact.order_index}
                          onChange={(e) => setNewContact(prev => ({ ...prev, order_index: parseInt(e.target.value) || 0 }))}
                          className="w-20 px-2 py-1 bg-white/5 border border-white/10 rounded focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-purple-500/20 hover:bg-purple-500/30"
                      >
                        {isSubmitting ? 'Creating...' : 'Create Contact'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={resetContactForm}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            {/* Emergency Contacts List */}
            {loading ? (
              <div className="text-center py-8 text-muted-foreground">
                Loading emergency contacts...
              </div>
            ) : emergencyContacts.length === 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>No emergency contacts found</CardTitle>
                  <CardDescription>
                    Add emergency contacts for this festival
                  </CardDescription>
                </CardHeader>
              </Card>
            ) : (
              <div className="grid gap-4">
                {emergencyContacts.map((contact) => (
                  <Card key={contact.id} className={!contact.is_active ? 'opacity-60' : ''}>
                    <CardHeader className="flex flex-row items-start justify-between space-y-0">
                      <div className="space-y-1 flex-1">
                        <div className="flex items-center gap-2">
                          <Phone className="h-5 w-5" />
                          <CardTitle className="text-lg">{contact.name}</CardTitle>
                          {contact.is_primary && (
                            <Badge variant="default" className="bg-yellow-500/20 text-yellow-400">
                              <Star className="mr-1 h-3 w-3" />
                              Primary
                            </Badge>
                          )}
                          <Badge
                            variant="secondary"
                            className={cn(contactTypeColors[contact.contact_type as keyof typeof contactTypeColors])}
                          >
                            {contact.contact_type.replace('_', ' ')}
                          </Badge>
                        </div>
                        <CardDescription>
                          {contact.phone && <div>📞 {contact.phone}</div>}
                          {contact.email && <div>✉️ {contact.email}</div>}
                          {contact.description && <div className="mt-1">{contact.description}</div>}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setEditingContact(contact)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteContact(contact.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="safety" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Safety Information</h2>
              <Button
                onClick={() => setShowSafetyForm(true)}
                className="bg-primary/20 hover:bg-primary/30"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Safety Info
              </Button>
            </div>

            {/* Create Safety Form */}
            {showSafetyForm && (
              <Card>
                <CardHeader>
                  <CardTitle>Add Safety Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCreateSafety} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Category</label>
                        <select
                          value={newSafety.safety_category}
                          onChange={(e) => setNewSafety(prev => ({ ...prev, safety_category: e.target.value as SafetyCategory }))}
                          className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          <option value="general">General</option>
                          <option value="medical">Medical</option>
                          <option value="security">Security</option>
                          <option value="weather">Weather</option>
                          <option value="transportation">Transportation</option>
                          <option value="emergency_procedures">Emergency Procedures</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Priority</label>
                        <select
                          value={newSafety.priority}
                          onChange={(e) => setNewSafety(prev => ({ ...prev, priority: e.target.value as Priority }))}
                          className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                          <option value="critical">Critical</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Title *</label>
                      <input
                        type="text"
                        placeholder="Safety information title"
                        value={newSafety.title}
                        onChange={(e) => setNewSafety(prev => ({ ...prev, title: e.target.value }))}
                        className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Content *</label>
                      <textarea
                        placeholder="Safety information content"
                        value={newSafety.content}
                        onChange={(e) => setNewSafety(prev => ({ ...prev, content: e.target.value }))}
                        className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-32"
                        required
                      />
                    </div>

                    <div className="flex items-center gap-4">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={newSafety.is_alert}
                          onChange={(e) => setNewSafety(prev => ({ ...prev, is_alert: e.target.checked }))}
                          className="rounded border-white/10 bg-white/5 text-red-500 focus:ring-red-500"
                        />
                        <span className="text-sm">Safety alert</span>
                      </label>
                      <div>
                        <label className="block text-sm font-medium mb-1">Order</label>
                        <input
                          type="number"
                          min="0"
                          value={newSafety.order_index}
                          onChange={(e) => setNewSafety(prev => ({ ...prev, order_index: parseInt(e.target.value) || 0 }))}
                          className="w-20 px-2 py-1 bg-white/5 border border-white/10 rounded focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-purple-500/20 hover:bg-purple-500/30"
                      >
                        {isSubmitting ? 'Creating...' : 'Create Safety Info'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={resetSafetyForm}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            {/* Safety Information List */}
            {loading ? (
              <div className="text-center py-8 text-muted-foreground">
                Loading safety information...
              </div>
            ) : safetyInfo.length === 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>No safety information found</CardTitle>
                  <CardDescription>
                    Add safety information for this festival
                  </CardDescription>
                </CardHeader>
              </Card>
            ) : (
              <div className="grid gap-4">
                {safetyInfo.map((safety) => (
                  <Card key={safety.id} className={!safety.is_active ? 'opacity-60' : ''}>
                    <CardHeader className="flex flex-row items-start justify-between space-y-0">
                      <div className="space-y-1 flex-1">
                        <div className="flex items-center gap-2">
                          {safety.is_alert ? (
                            <AlertTriangle className="h-5 w-5 text-red-400" />
                          ) : (
                            <Shield className="h-5 w-5" />
                          )}
                          <CardTitle className="text-lg">{safety.title}</CardTitle>
                          <Badge
                            variant="secondary"
                            className={cn(safetyPriorityColors[safety.priority as keyof typeof safetyPriorityColors])}
                          >
                            {safety.priority}
                          </Badge>
                          {safety.is_alert && (
                            <Badge variant="destructive">
                              Alert
                            </Badge>
                          )}
                        </div>
                        <CardDescription>
                          <div className="mb-2 text-sm text-purple-400">
                            {safety.safety_category.replace('_', ' ')}
                          </div>
                          <div>{safety.content}</div>
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setEditingSafety(safety)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteSafety(safety.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default AdminEmergencyManagement;
