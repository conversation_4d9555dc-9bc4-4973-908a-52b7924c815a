<testsuites id="" name="" tests="36" failures="31" skipped="5" errors="0" time="208.145508">
<testsuite name="activity-cards-audit.spec.ts" timestamp="2025-06-08T22:49:09.258Z" hostname="chromium" tests="6" failures="5" skipped="1" time="138.006" errors="0">
<testcase name="Activity Cards Interactive Functionality Audit › should display activity cards with real database data" classname="activity-cards-audit.spec.ts" time="25.275">
<failure message="activity-cards-audit.spec.ts:15:3 should display activity cards with real database data" type="FAILURE">
<![CDATA[  [chromium] › activity-cards-audit.spec.ts:15:3 › Activity Cards Interactive Functionality Audit › should display activity cards with real database data 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Join Activity button functionality" classname="activity-cards-audit.spec.ts" time="27.262">
<failure message="activity-cards-audit.spec.ts:32:3 should test Join Activity button functionality" type="FAILURE">
<![CDATA[  [chromium] › activity-cards-audit.spec.ts:32:3 › Activity Cards Interactive Functionality Audit › should test Join Activity button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-chromium\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Details button functionality" classname="activity-cards-audit.spec.ts" time="26.739">
<failure message="activity-cards-audit.spec.ts:62:3 should test Details button functionality" type="FAILURE">
<![CDATA[  [chromium] › activity-cards-audit.spec.ts:62:3 › Activity Cards Interactive Functionality Audit › should test Details button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality" classname="activity-cards-audit.spec.ts" time="24.042">
<failure message="activity-cards-audit.spec.ts:92:3 should test Favorites/Heart button functionality" type="FAILURE">
<![CDATA[  [chromium] › activity-cards-audit.spec.ts:92:3 › Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should verify card layout and styling" classname="activity-cards-audit.spec.ts" time="24.5">
<failure message="activity-cards-audit.spec.ts:116:3 should verify card layout and styling" type="FAILURE">
<![CDATA[  [chromium] › activity-cards-audit.spec.ts:116:3 › Activity Cards Interactive Functionality Audit › should verify card layout and styling 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-chromium\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-chromium\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test responsive design" classname="activity-cards-audit.spec.ts" time="10.188">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="activity-cards-audit.spec.ts" timestamp="2025-06-08T22:49:09.258Z" hostname="firefox" tests="6" failures="6" skipped="0" time="0.199" errors="0">
<testcase name="Activity Cards Interactive Functionality Audit › should display activity cards with real database data" classname="activity-cards-audit.spec.ts" time="0.019">
<failure message="activity-cards-audit.spec.ts:15:3 should display activity cards with real database data" type="FAILURE">
<![CDATA[  [firefox] › activity-cards-audit.spec.ts:15:3 › Activity Cards Interactive Functionality Audit › should display activity cards with real database data 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Join Activity button functionality" classname="activity-cards-audit.spec.ts" time="0.018">
<failure message="activity-cards-audit.spec.ts:32:3 should test Join Activity button functionality" type="FAILURE">
<![CDATA[  [firefox] › activity-cards-audit.spec.ts:32:3 › Activity Cards Interactive Functionality Audit › should test Join Activity button functionality 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Details button functionality" classname="activity-cards-audit.spec.ts" time="0.051">
<failure message="activity-cards-audit.spec.ts:62:3 should test Details button functionality" type="FAILURE">
<![CDATA[  [firefox] › activity-cards-audit.spec.ts:62:3 › Activity Cards Interactive Functionality Audit › should test Details button functionality 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality" classname="activity-cards-audit.spec.ts" time="0.05">
<failure message="activity-cards-audit.spec.ts:92:3 should test Favorites/Heart button functionality" type="FAILURE">
<![CDATA[  [firefox] › activity-cards-audit.spec.ts:92:3 › Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should verify card layout and styling" classname="activity-cards-audit.spec.ts" time="0.023">
<failure message="activity-cards-audit.spec.ts:116:3 should verify card layout and styling" type="FAILURE">
<![CDATA[  [firefox] › activity-cards-audit.spec.ts:116:3 › Activity Cards Interactive Functionality Audit › should verify card layout and styling 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test responsive design" classname="activity-cards-audit.spec.ts" time="0.038">
<failure message="activity-cards-audit.spec.ts:145:3 should test responsive design" type="FAILURE">
<![CDATA[  [firefox] › activity-cards-audit.spec.ts:145:3 › Activity Cards Interactive Functionality Audit › should test responsive design 

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1482\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-feb38-ould-test-responsive-design-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-feb38-ould-test-responsive-design-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="activity-cards-audit.spec.ts" timestamp="2025-06-08T22:49:09.258Z" hostname="webkit" tests="6" failures="5" skipped="1" time="149.341" errors="0">
<testcase name="Activity Cards Interactive Functionality Audit › should display activity cards with real database data" classname="activity-cards-audit.spec.ts" time="28.311">
<failure message="activity-cards-audit.spec.ts:15:3 should display activity cards with real database data" type="FAILURE">
<![CDATA[  [webkit] › activity-cards-audit.spec.ts:15:3 › Activity Cards Interactive Functionality Audit › should display activity cards with real database data 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.
    =========================== logs ===========================
      "domcontentloaded" event fired
    ============================================================

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Join Activity button functionality" classname="activity-cards-audit.spec.ts" time="30.03">
<failure message="activity-cards-audit.spec.ts:32:3 should test Join Activity button functionality" type="FAILURE">
<![CDATA[  [webkit] › activity-cards-audit.spec.ts:32:3 › Activity Cards Interactive Functionality Audit › should test Join Activity button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-webkit\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Details button functionality" classname="activity-cards-audit.spec.ts" time="26.757">
<failure message="activity-cards-audit.spec.ts:62:3 should test Details button functionality" type="FAILURE">
<![CDATA[  [webkit] › activity-cards-audit.spec.ts:62:3 › Activity Cards Interactive Functionality Audit › should test Details button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality" classname="activity-cards-audit.spec.ts" time="26.396">
<failure message="activity-cards-audit.spec.ts:92:3 should test Favorites/Heart button functionality" type="FAILURE">
<![CDATA[  [webkit] › activity-cards-audit.spec.ts:92:3 › Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.
    =========================== logs ===========================
      "domcontentloaded" event fired
    ============================================================

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should verify card layout and styling" classname="activity-cards-audit.spec.ts" time="27.284">
<failure message="activity-cards-audit.spec.ts:116:3 should verify card layout and styling" type="FAILURE">
<![CDATA[  [webkit] › activity-cards-audit.spec.ts:116:3 › Activity Cards Interactive Functionality Audit › should verify card layout and styling 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.
    =========================== logs ===========================
      "domcontentloaded" event fired
    ============================================================

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test responsive design" classname="activity-cards-audit.spec.ts" time="10.563">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="activity-cards-audit.spec.ts" timestamp="2025-06-08T22:49:09.258Z" hostname="Mobile Chrome" tests="6" failures="5" skipped="1" time="132.903" errors="0">
<testcase name="Activity Cards Interactive Functionality Audit › should display activity cards with real database data" classname="activity-cards-audit.spec.ts" time="25.062">
<failure message="activity-cards-audit.spec.ts:15:3 should display activity cards with real database data" type="FAILURE">
<![CDATA[  [Mobile Chrome] › activity-cards-audit.spec.ts:15:3 › Activity Cards Interactive Functionality Audit › should display activity cards with real database data 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Chrome\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Join Activity button functionality" classname="activity-cards-audit.spec.ts" time="23.751">
<failure message="activity-cards-audit.spec.ts:32:3 should test Join Activity button functionality" type="FAILURE">
<![CDATA[  [Mobile Chrome] › activity-cards-audit.spec.ts:32:3 › Activity Cards Interactive Functionality Audit › should test Join Activity button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Details button functionality" classname="activity-cards-audit.spec.ts" time="26.678">
<failure message="activity-cards-audit.spec.ts:62:3 should test Details button functionality" type="FAILURE">
<![CDATA[  [Mobile Chrome] › activity-cards-audit.spec.ts:62:3 › Activity Cards Interactive Functionality Audit › should test Details button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Chrome\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality" classname="activity-cards-audit.spec.ts" time="24.599">
<failure message="activity-cards-audit.spec.ts:92:3 should test Favorites/Heart button functionality" type="FAILURE">
<![CDATA[  [Mobile Chrome] › activity-cards-audit.spec.ts:92:3 › Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Chrome\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should verify card layout and styling" classname="activity-cards-audit.spec.ts" time="24.426">
<failure message="activity-cards-audit.spec.ts:116:3 should verify card layout and styling" type="FAILURE">
<![CDATA[  [Mobile Chrome] › activity-cards-audit.spec.ts:116:3 › Activity Cards Interactive Functionality Audit › should verify card layout and styling 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Chrome\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test responsive design" classname="activity-cards-audit.spec.ts" time="8.387">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="activity-cards-audit.spec.ts" timestamp="2025-06-08T22:49:09.258Z" hostname="Mobile Safari" tests="6" failures="5" skipped="1" time="148.091" errors="0">
<testcase name="Activity Cards Interactive Functionality Audit › should display activity cards with real database data" classname="activity-cards-audit.spec.ts" time="28.555">
<failure message="activity-cards-audit.spec.ts:15:3 should display activity cards with real database data" type="FAILURE">
<![CDATA[  [Mobile Safari] › activity-cards-audit.spec.ts:15:3 › Activity Cards Interactive Functionality Audit › should display activity cards with real database data 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.
    =========================== logs ===========================
      "domcontentloaded" event fired
    ============================================================

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Join Activity button functionality" classname="activity-cards-audit.spec.ts" time="29.465">
<failure message="activity-cards-audit.spec.ts:32:3 should test Join Activity button functionality" type="FAILURE">
<![CDATA[  [Mobile Safari] › activity-cards-audit.spec.ts:32:3 › Activity Cards Interactive Functionality Audit › should test Join Activity button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Safari\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Details button functionality" classname="activity-cards-audit.spec.ts" time="26.107">
<failure message="activity-cards-audit.spec.ts:62:3 should test Details button functionality" type="FAILURE">
<![CDATA[  [Mobile Safari] › activity-cards-audit.spec.ts:62:3 › Activity Cards Interactive Functionality Audit › should test Details button functionality 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.
    =========================== logs ===========================
      "domcontentloaded" event fired
    ============================================================

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality" classname="activity-cards-audit.spec.ts" time="26.347">
<failure message="activity-cards-audit.spec.ts:92:3 should test Favorites/Heart button functionality" type="FAILURE">
<![CDATA[  [Mobile Safari] › activity-cards-audit.spec.ts:92:3 › Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.
    =========================== logs ===========================
      "domcontentloaded" event fired
    ============================================================

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should verify card layout and styling" classname="activity-cards-audit.spec.ts" time="26.978">
<failure message="activity-cards-audit.spec.ts:116:3 should verify card layout and styling" type="FAILURE">
<![CDATA[  [Mobile Safari] › activity-cards-audit.spec.ts:116:3 › Activity Cards Interactive Functionality Audit › should verify card layout and styling 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.
    =========================== logs ===========================
      "domcontentloaded" event fired
    ============================================================

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test responsive design" classname="activity-cards-audit.spec.ts" time="10.639">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="activity-cards-audit.spec.ts" timestamp="2025-06-08T22:49:09.258Z" hostname="Tablet" tests="6" failures="5" skipped="1" time="143.312" errors="0">
<testcase name="Activity Cards Interactive Functionality Audit › should display activity cards with real database data" classname="activity-cards-audit.spec.ts" time="24.865">
<failure message="activity-cards-audit.spec.ts:15:3 should display activity cards with real database data" type="FAILURE">
<![CDATA[  [Tablet] › activity-cards-audit.spec.ts:15:3 › Activity Cards Interactive Functionality Audit › should display activity cards with real database data 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-f8e87-rds-with-real-database-data-Tablet\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Join Activity button functionality" classname="activity-cards-audit.spec.ts" time="28.973">
<failure message="activity-cards-audit.spec.ts:32:3 should test Join Activity button functionality" type="FAILURE">
<![CDATA[  [Tablet] › activity-cards-audit.spec.ts:32:3 › Activity Cards Interactive Functionality Audit › should test Join Activity button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Tablet\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-7fecb-tivity-button-functionality-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Details button functionality" classname="activity-cards-audit.spec.ts" time="28.472">
<failure message="activity-cards-audit.spec.ts:62:3 should test Details button functionality" type="FAILURE">
<![CDATA[  [Tablet] › activity-cards-audit.spec.ts:62:3 › Activity Cards Interactive Functionality Audit › should test Details button functionality 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Tablet\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-49e71-etails-button-functionality-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality" classname="activity-cards-audit.spec.ts" time="23.831">
<failure message="activity-cards-audit.spec.ts:92:3 should test Favorites/Heart button functionality" type="FAILURE">
<![CDATA[  [Tablet] › activity-cards-audit.spec.ts:92:3 › Activity Cards Interactive Functionality Audit › should test Favorites/Heart button functionality 

    TimeoutError: page.waitForLoadState: Timeout 20000ms exceeded.

       7 |     
       8 |     // Wait for the page to load
    >  9 |     await page.waitForLoadState('networkidle');
         |                ^
      10 |     
      11 |     // Wait for activities to load (look for activity cards or loading state)
      12 |     await page.waitForSelector('[data-testid="activity-card"], .animate-spin, text="No activities found"', { timeout: 10000 });
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Tablet\test-failed-1.png]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-b9ac4--Heart-button-functionality-Tablet\video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should verify card layout and styling" classname="activity-cards-audit.spec.ts" time="28.551">
<failure message="activity-cards-audit.spec.ts:116:3 should verify card layout and styling" type="FAILURE">
<![CDATA[  [Tablet] › activity-cards-audit.spec.ts:116:3 › Activity Cards Interactive Functionality Audit › should verify card layout and styling 

    TimeoutError: page.goto: Timeout 20000ms exceeded.
    Call log:
      - navigating to "http://localhost:5173/activities", waiting until "load"


      4 |   test.beforeEach(async ({ page }) => {
      5 |     // Navigate to the activities page
    > 6 |     await page.goto('http://localhost:5173/activities');
        |                ^
      7 |     
      8 |     // Wait for the page to load
      9 |     await page.waitForLoadState('networkidle');
        at C:\Users\<USER>\CascadeProjects\festival-family\tests\activity-cards-audit.spec.ts:6:16

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Tablet\video.webm]]

[[ATTACHMENT|artifacts\activity-cards-audit-Activ-08a0a-ify-card-layout-and-styling-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Activity Cards Interactive Functionality Audit › should test responsive design" classname="activity-cards-audit.spec.ts" time="8.62">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>