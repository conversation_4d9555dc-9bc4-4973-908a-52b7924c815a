import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, Megaphone, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import type { DatabaseAnnouncement } from '@/types/database';

interface Announcement extends Omit<DatabaseAnnouncement, 'display_type'> {
  priority?: 'low' | 'medium' | 'high';
  type?: 'info' | 'warning' | 'success' | 'error' | 'urgent';
  display_type?: 'banner' | 'popup' | 'toast';
  is_pinned?: boolean;
  is_featured?: boolean;
}

interface GlobalAnnouncementBannerProps {
  className?: string;
}

export const GlobalAnnouncementBanner: React.FC<GlobalAnnouncementBannerProps> = ({ 
  className = '' 
}) => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [dismissed, setDismissed] = useState<Set<string>>(new Set());
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Load dismissed announcements from localStorage first
    const dismissedIds = localStorage.getItem('dismissed_announcements');
    if (dismissedIds) {
      setDismissed(new Set(JSON.parse(dismissedIds)));
    }
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (!isInitialized) return;

    fetchActiveAnnouncements();

    // Refresh announcements every 30 seconds
    const interval = setInterval(fetchActiveAnnouncements, 30000);
    return () => clearInterval(interval);
  }, [isInitialized, dismissed]);

  const fetchActiveAnnouncements = async () => {
    try {
      const currentTime = new Date().toISOString();
      
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .eq('active', true)
        .or('display_type.eq.banner,display_type.is.null')
        .or(`start_date.is.null,start_date.lte.${currentTime}`)
        .or(`end_date.is.null,end_date.gte.${currentTime}`)
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        console.error('Error fetching announcements:', error);
        return;
      }

      if (data) {
        // Filter out dismissed announcements and add default values with null safety
        const activeAnnouncements = data
          .filter(announcement => !dismissed.has(announcement.id.toString()))
          .map(announcement => ({
            ...announcement,
            priority: (announcement.priority as 'low' | 'medium' | 'high') ?? 'medium',
            type: (announcement.type as 'info' | 'warning' | 'success' | 'error' | 'urgent') ?? 'info',
            display_type: (announcement.display_type as 'banner' | 'popup' | 'toast') ?? 'banner',
            active: announcement.active ?? true,
            is_pinned: announcement.is_pinned ?? false
          }));
        setAnnouncements(activeAnnouncements);
      }
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  const dismissAnnouncement = (id: string) => {
    const newDismissed = new Set(dismissed);
    newDismissed.add(id);
    setDismissed(newDismissed);

    // Save to localStorage
    localStorage.setItem('dismissed_announcements', JSON.stringify([...newDismissed]));

    // Remove from current announcements
    setAnnouncements(prev => prev.filter(ann => ann.id !== id));

    // Adjust current index if needed
    if (currentIndex >= announcements.length - 1) {
      setCurrentIndex(0);
    }
  };

  const getAnnouncementVariant = (type: string, priority: string) => {
    if (priority === 'high' || type === 'urgent' || type === 'error') {
      return 'destructive';
    }
    if (type === 'warning') {
      return 'warning';
    }
    return 'default';
  };

  const getAnnouncementIcon = (type: string, priority: string) => {
    switch (type) {
      case 'error':
      case 'urgent':
        return AlertTriangle;
      case 'warning':
        return AlertTriangle;
      case 'success':
        return CheckCircle;
      case 'info':
      default:
        return priority === 'high' ? Megaphone : Info;
    }
  };

  // Cycle through announcements every 10 seconds if there are multiple
  useEffect(() => {
    if (announcements.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % announcements.length);
      }, 10000);
      return () => clearInterval(interval);
    }
  }, [announcements.length]);

  if (announcements.length === 0) {
    return null;
  }

  const currentAnnouncement = announcements[currentIndex];
  if (!currentAnnouncement) {
    return null;
  }

  const variant = getAnnouncementVariant(currentAnnouncement.type || 'info', currentAnnouncement.priority || 'medium');
  const IconComponent = getAnnouncementIcon(currentAnnouncement.type || 'info', currentAnnouncement.priority || 'medium');

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={currentAnnouncement.id}
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        transition={{ duration: 0.3 }}
        className={`w-full ${className}`}
      >
        <Alert variant={variant} className="rounded-none border-x-0 border-t-0">
          <IconComponent className="h-4 w-4" />
          <div className="flex items-start justify-between w-full">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <AlertTitle className="text-sm font-medium">
                  {currentAnnouncement.title}
                </AlertTitle>
                <Badge 
                  variant="outline" 
                  className="text-xs capitalize"
                >
                  {currentAnnouncement.priority}
                </Badge>
                {currentAnnouncement.is_pinned && (
                  <Badge variant="secondary" className="text-xs">
                    Pinned
                  </Badge>
                )}
                {currentAnnouncement.is_featured && (
                  <Badge variant="secondary" className="text-xs">
                    Featured
                  </Badge>
                )}
              </div>
              <AlertDescription className="text-sm">
                {currentAnnouncement.content}
              </AlertDescription>
              {announcements.length > 1 && (
                <div className="flex items-center gap-1 mt-2">
                  {announcements.map((announcement, index) => (
                    <div
                      key={`indicator-${announcement.id}`}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentIndex
                          ? 'bg-current'
                          : 'bg-current opacity-30'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => dismissAnnouncement(currentAnnouncement.id)}
              className="ml-4 shrink-0 h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </Alert>
      </motion.div>
    </AnimatePresence>
  );
};
