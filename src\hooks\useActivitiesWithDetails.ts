/**
 * Custom hook for fetching activities with their specialized details
 *
 * This hook fetches activities and their related meetup or workshop details
 * using the cascading data structure. It also supports hierarchical relationships
 * between parent and child activities.
 */

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { ActivityWithDetails } from '@/types/activities';
import { normalizeActivityType } from '@/types/activityTypes';
import {
  combineActivitiesWithDetails,
  SupabaseActivity,
  SupabaseMeetup,
  SupabaseWorkshop
} from '@/lib/utils/activityConverters';

interface UseActivitiesWithDetailsProps {
  festivalId?: string;
  activityType?: string;
  featured?: boolean;
}

/**
 * Hook for fetching activities with their specialized details
 *
 * @param props - Options for filtering activities
 * @returns Object containing activities with details, loading state, error state, and utility functions
 */
export function useActivitiesWithDetails(props?: UseActivitiesWithDetailsProps) {
  const { festivalId, activityType, featured } = props || {};

  const [activities, setActivities] = useState<SupabaseActivity[]>([]);
  const [meetups, setMeetups] = useState<SupabaseMeetup[]>([]);
  const [workshops, setWorkshops] = useState<SupabaseWorkshop[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Define fetchActivities outside useEffect for reuse in refetch function
  const fetchActivities = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Build the query based on provided filters
      let query = supabase.from('activities').select('*');

      // Apply filters if provided
      if (festivalId) {
        query = query.eq('festival_id', festivalId);
      }

      if (activityType) {
        // Normalize the activity type to ensure consistent filtering
        const normalizedType = normalizeActivityType(activityType);
        query = query.eq('type', normalizedType as any);
      }

      // Apply is_featured filter if provided
      if (featured !== undefined) {
        query = query.eq('is_featured', featured);
      }

      // Order by parent_activity_id (nulls first) and then by created_at
      query = query.order('parent_activity_id', { ascending: true, nullsFirst: true })
             .order('created_at', { ascending: false });

      const { data: activitiesData, error: activitiesError } = await query;

      if (activitiesError) throw activitiesError;

      // Store the activities data
      setActivities(activitiesData || []);

      // For now, skip specialized details fetching since we don't have separate meetups/workshops tables
      // The activities table contains all the data we need
      // This can be extended later when specialized tables are added
      setMeetups([]);
      setWorkshops([]);

      return activitiesData;
    } catch (err) {
      const newError = err instanceof Error ? err : new Error('Unknown error');
      setError(newError);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchActivities();
  }, [festivalId, activityType, featured]);

  // Provide a refetch function to manually trigger data refresh
  const refetch = async () => {
    return await fetchActivities();
  };

  // Combine activities with their specialized details
  const activitiesWithDetails: ActivityWithDetails[] = combineActivitiesWithDetails(
    activities,
    meetups,
    workshops
  );

  return {
    // Raw data
    activities,
    meetups,
    workshops,
    // Combined data with proper type handling
    activitiesWithDetails,
    // Status
    isLoading,
    error,
    // Utility functions
    refetch
  };
}