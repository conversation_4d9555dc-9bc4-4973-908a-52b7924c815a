import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/providers/ConsolidatedAuthProvider';
import { userInteractionsService } from '@/lib/supabase/services/user-interactions-service';
import { toast } from 'react-hot-toast';

export interface UserInteractionState {
  isJoined: boolean;
  isFavorited: boolean;
  isLoading: boolean;
  joinLoading: boolean;
  favoriteLoading: boolean;
}

export function useUserInteractions(activityId: string) {
  const { user } = useAuth();
  const [state, setState] = useState<UserInteractionState>({
    isJoined: false,
    isFavorited: false,
    isLoading: true,
    joinLoading: false,
    favoriteLoading: false,
  });

  // Load initial state
  useEffect(() => {
    if (user && activityId) {
      loadInteractionState();
    } else {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [user, activityId]);

  const loadInteractionState = async () => {
    if (!user) return;

    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const [joinedResult, favoritedResult] = await Promise.all([
        userInteractionsService.isUserJoined(activityId, user.id),
        userInteractionsService.isFavorited(activityId, user.id),
      ]);

      setState(prev => ({
        ...prev,
        isJoined: joinedResult.data || false,
        isFavorited: favoritedResult.data || false,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading interaction state:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const toggleJoin = useCallback(async () => {
    if (!user) {
      toast.error('Please log in to join activities');
      return;
    }

    try {
      setState(prev => ({ ...prev, joinLoading: true }));

      if (state.isJoined) {
        // Leave activity
        const result = await userInteractionsService.leaveActivity(activityId, user.id);
        if (result.status === 'success') {
          setState(prev => ({ ...prev, isJoined: false }));
          toast.success('Left activity successfully');
        } else {
          throw result.error;
        }
      } else {
        // Join activity
        const result = await userInteractionsService.joinActivity(activityId, user.id);
        if (result.status === 'success') {
          setState(prev => ({ ...prev, isJoined: true }));
          toast.success('Joined activity successfully!');
        } else {
          throw result.error;
        }
      }
    } catch (error) {
      console.error('Error toggling join:', error);
      toast.error(state.isJoined ? 'Failed to leave activity' : 'Failed to join activity');
    } finally {
      setState(prev => ({ ...prev, joinLoading: false }));
    }
  }, [user, activityId, state.isJoined]);

  const toggleFavorite = useCallback(async () => {
    if (!user) {
      toast.error('Please log in to favorite activities');
      return;
    }

    try {
      setState(prev => ({ ...prev, favoriteLoading: true }));

      if (state.isFavorited) {
        // Remove from favorites
        const result = await userInteractionsService.removeFromFavorites(activityId, user.id);
        if (result.status === 'success') {
          setState(prev => ({ ...prev, isFavorited: false }));
          toast.success('Removed from favorites');
        } else {
          throw result.error;
        }
      } else {
        // Add to favorites
        const result = await userInteractionsService.addToFavorites(activityId, user.id);
        if (result.status === 'success') {
          setState(prev => ({ ...prev, isFavorited: true }));
          toast.success('Added to favorites!');
        } else {
          throw result.error;
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error(state.isFavorited ? 'Failed to remove from favorites' : 'Failed to add to favorites');
    } finally {
      setState(prev => ({ ...prev, favoriteLoading: false }));
    }
  }, [user, activityId, state.isFavorited]);

  const recordView = useCallback(async () => {
    try {
      await userInteractionsService.recordView(activityId, user?.id);
    } catch (error) {
      // Silently fail for view recording
      console.warn('Failed to record activity view:', error);
    }
  }, [user, activityId]);

  return {
    ...state,
    toggleJoin,
    toggleFavorite,
    recordView,
    refresh: loadInteractionState,
  };
}

export function useUserParticipations() {
  const { user } = useAuth();
  const [participations, setParticipations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadParticipations();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadParticipations = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const result = await userInteractionsService.getUserParticipations(user.id);
      if (result.status === 'success') {
        setParticipations(result.data || []);
      }
    } catch (error) {
      console.error('Error loading participations:', error);
    } finally {
      setLoading(false);
    }
  };

  return {
    participations,
    loading,
    refresh: loadParticipations,
  };
}

export function useUserFavorites() {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadFavorites();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadFavorites = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const result = await userInteractionsService.getUserFavorites(user.id);
      if (result.status === 'success') {
        setFavorites(result.data || []);
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  return {
    favorites,
    loading,
    refresh: loadFavorites,
  };
}
