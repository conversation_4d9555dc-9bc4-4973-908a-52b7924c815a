import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { IoChatboxOutline, IoPeopleOutline, IoBookOutline, IoInformationCircleOutline, IoGrid, IoList } from 'react-icons/io5';
import { IconType } from 'react-icons';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ErrorBoundary } from 'react-error-boundary';
import { AlertCircle, RefreshCw, Search, Filter, Heart, Share2, Users, MapPin, Clock, Star, X } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { simulateHapticFeedback, isMobileViewport, createTouchHand<PERSON> } from '../utils/mobileUX';
import MobileUXTester from '../components/testing/MobileUXTester';
import { supabase } from '@/lib/supabase';
import TipDetailsModal from '@/components/tips/TipDetailsModal';
import GuideDetailsModal from '@/components/guides/GuideDetailsModal';
import FAQDetailsModal from '@/components/faqs/FAQDetailsModal';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';

type Tab = 'CHAT' | 'COMMUNITIES' | 'RESOURCES' | 'LOCAL_INFO';
type ViewMode = 'grid' | 'list';

interface FilterTab {
  id: Tab;
  label: string;
  icon: IconType;
}

const filterTabs: FilterTab[] = [
  { id: 'CHAT', label: 'Chat', icon: IoChatboxOutline },
  { id: 'COMMUNITIES', label: 'Communities', icon: IoPeopleOutline },
  { id: 'RESOURCES', label: 'Resources', icon: IoBookOutline },
  { id: 'LOCAL_INFO', label: 'Local Info', icon: IoInformationCircleOutline },
];

// Temporary Chat Section Component
const ChatSection = () => {
  const [chatLinks, setChatLinks] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchChatLinks = async () => {
      try {
        const { data, error } = await supabase
          .from('external_links')
          .select('*')
          .eq('active', true)
          .in('category', ['CHAT', 'SOCIAL'])
          .order('created_at', { ascending: false });

        if (error) throw error;
        setChatLinks(data || []);
      } catch (error) {
        console.error('Error fetching chat links:', error);
        // Fallback to default chat options
        setChatLinks([
          {
            id: 'whatsapp',
            title: 'WhatsApp Group',
            description: 'Join our main WhatsApp community for real-time chat',
            url: 'https://wa.me/groupinvite',
            category: 'CHAT'
          },
          {
            id: 'discord',
            title: 'Discord Server',
            description: 'Connect on Discord for voice and text chat',
            url: 'https://discord.gg/festivalfamily',
            category: 'CHAT'
          },
          {
            id: 'telegram',
            title: 'Telegram Channel',
            description: 'Get updates and chat on Telegram',
            url: 'https://t.me/festivalfamily',
            category: 'CHAT'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchChatLinks();
  }, []);

  if (loading) {
    return (
      <Card className="bg-white/10 backdrop-blur-md border border-white/20 text-white">
        <CardContent className="p-6 text-center">
          <div className="text-white/60">Loading chat options...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <IoChatboxOutline className="w-8 h-8 mx-auto text-purple-400 mb-2" />
        <h3 className="text-lg font-semibold text-white">Festival Chat Groups</h3>
        <p className="text-white/70 text-sm">Connect with fellow festival-goers on your favorite platform</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {chatLinks.map((chat) => (
          <Card key={chat.id} className="bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/15 transition-colors">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-white">{chat.title}</h4>
                <Badge variant="outline" className="text-xs border-purple-400 text-purple-400">
                  {chat.category}
                </Badge>
              </div>
              <p className="text-white/70 text-sm mb-4 line-clamp-2">
                {chat.description || 'Join this chat group to connect with other festival enthusiasts'}
              </p>
              <Button
                onClick={() => window.open(chat.url, '_blank', 'noopener,noreferrer')}
                className="w-full bg-purple-700 hover:bg-purple-600"
                size="sm"
              >
                Join Chat
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {chatLinks.length === 0 && (
        <Card className="bg-white/10 backdrop-blur-md border border-white/20 text-white">
          <CardContent className="p-6 text-center">
            <IoChatboxOutline className="w-12 h-12 mx-auto text-purple-400 mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Chat Groups Available</h3>
            <p className="text-white/70">Check back later for chat group links</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Enhanced Mobile-First Communities Section Component
const CommunitiesSection = ({
  viewMode,
  searchQuery,
  favorites,
  onFavoriteToggle,
  isMobile
}: {
  viewMode: ViewMode;
  searchQuery: string;
  favorites: Set<string>;
  onFavoriteToggle: (id: string) => void;
  isMobile: boolean;
}) => {
  const [communities, setCommunities] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);

  // Fetch real communities from database using groups and external_links tables
  React.useEffect(() => {
    const fetchCommunities = async () => {
      try {
        // Try to fetch from communities table first, then fallback to groups and external_links
        const { data: communitiesData, error: communitiesError } = await supabase
          .from('communities')
          .select('*')
          .eq('is_active', true)
          .order('is_featured', { ascending: false })
          .order('member_count', { ascending: false });

        if (communitiesData && !communitiesError) {
          // Use the dedicated communities table
          const transformedCommunities = communitiesData.map(community => ({
            id: community.id,
            name: community.name,
            description: community.description || 'Join this community to connect with fellow festival-goers.',
            members: community.member_count || 0,
            category: community.category || 'General',
            featured: community.is_featured || false,
            externalLink: community.external_url,
            type: community.type || 'external'
          }));
          setCommunities(transformedCommunities);
          return;
        }

        // Fallback: Fetch both groups and external community links
        const [groupsResponse, linksResponse] = await Promise.all([
          supabase
            .from('groups')
            .select('*')
            .order('created_at', { ascending: false }),
          supabase
            .from('external_links')
            .select('*')
            .eq('active', true)
            .in('category', ['COMMUNITY', 'SOCIAL', 'DISCORD', 'TELEGRAM'])
            .order('created_at', { ascending: false })
        ]);

        const groups = groupsResponse.data || [];
        const links = linksResponse.data || [];

        // Transform groups to communities format
        const groupCommunities = groups.map(group => ({
          id: `group-${group.id}`,
          name: group.name || 'Festival Group',
          description: group.description || 'Join this group to connect with fellow festival-goers.',
          members: 0, // We don't have member count for groups yet
          category: 'Groups',
          featured: false,
          externalLink: null,
          type: 'group'
        }));

        // Transform external links to communities format
        const linkCommunities = links.map(link => ({
          id: `link-${link.id}`,
          name: link.title,
          description: link.description || `Join our ${link.category?.toLowerCase() || 'community'} community`,
          members: 0,
          category: link.category,
          featured: false,
          externalLink: link.url,
          type: 'external'
        }));

        // Combine and add fallback communities if none exist
        const allCommunities = [...groupCommunities, ...linkCommunities];
        
        if (allCommunities.length === 0) {
          // Fallback communities for production
          setCommunities([
            {
              id: 'discord-main',
              name: 'Main Discord Server',
              description: 'Join our main Discord community for voice and text chat with fellow festival enthusiasts',
              members: 150,
              category: 'Discord',
              featured: true,
              externalLink: 'https://discord.gg/festivalfamily',
              type: 'external'
            },
            {
              id: 'telegram-updates',
              name: 'Telegram Updates',
              description: 'Get real-time updates and join discussions on Telegram',
              members: 89,
              category: 'Telegram',
              featured: false,
              externalLink: 'https://t.me/festivalfamily',
              type: 'external'
            },
            {
              id: 'facebook-group',
              name: 'Facebook Community',
              description: 'Connect on Facebook and share your festival experiences',
              members: 234,
              category: 'Facebook',
              featured: false,
              externalLink: 'https://facebook.com/groups/festivalfamily',
              type: 'external'
            }
          ]);
        } else {
          setCommunities(allCommunities);
        }
      } catch (error) {
        console.error('Error fetching communities:', error);
        // Fallback to default communities on error
        setCommunities([
          {
            id: 'discord-fallback',
            name: 'Discord Community',
            description: 'Join our Discord server to connect with other festival lovers',
            members: 0,
            category: 'Discord',
            featured: true,
            externalLink: 'https://discord.gg/festivalfamily',
            type: 'external'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchCommunities();
  }, []);

  const filteredCommunities = communities.filter(community =>
    community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    community.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCommunityClick = useCallback((community: any) => {
    simulateHapticFeedback('light');

    // If community has external link, open it
    if (community.externalLink?.url) {
      window.open(community.externalLink.url, '_blank', 'noopener,noreferrer');
      toast.success(`Opening ${community.externalLink.title || community.name}...`);
    } else {
      toast.success('Opening community...');
    }
  }, []);

  const handleJoinCommunity = useCallback((e: React.MouseEvent, community: any) => {
    e.stopPropagation();
    simulateHapticFeedback('medium');

    // If community has external link, open it
    if (community.externalLink) {
      window.open(community.externalLink, '_blank', 'noopener,noreferrer');
      toast.success(`Joining ${community.name}!`);
    } else if (community.type === 'group') {
      // Handle internal group joining (could link to group page later)
      toast.success(`Joined ${community.name}!`);
    } else {
      toast.success('Joined community!');
    }
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="bg-white/10 backdrop-blur-md border border-white/20 text-white h-full animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-white/20 rounded mb-2"></div>
              <div className="h-3 bg-white/10 rounded mb-4"></div>
              <div className="h-8 bg-white/10 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid gap-4 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
      {filteredCommunities.map((community, index) => (
        <motion.div
          key={community.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="group"
        >
          <Card
            className="bg-white/10 backdrop-blur-md border border-white/20 text-white h-full cursor-pointer hover:bg-white/20 transition-all duration-300 overflow-hidden"
            {...createTouchHandler(() => handleCommunityClick(community))}
          >
            <CardContent className={`p-4 ${viewMode === 'list' ? 'flex items-center gap-4' : 'flex flex-col gap-4'}`}>
              {/* Community Header */}
              <div className={`flex items-start gap-4 ${viewMode === 'list' ? 'flex-1' : 'w-full'}`}>
                <div className="relative">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-lg bg-gradient-to-br from-purple-700/30 to-purple-600/30 flex-shrink-0 flex items-center justify-center">
                    <IoPeopleOutline className="w-6 h-6 sm:w-8 sm:h-8 text-white/70" />
                  </div>
                  {community.featured && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                      <Star className="w-3 h-3 text-white" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <CardTitle className="text-base sm:text-lg font-semibold truncate">{community.name}</CardTitle>
                    <motion.button
                      onClick={(e) => {
                        e.stopPropagation();
                        onFavoriteToggle(community.id);
                      }}
                      className="p-1 hover:bg-white/10 rounded-full transition-colors"
                      whileTap={{ scale: 0.9 }}
                    >
                      <Heart
                        className={`w-4 h-4 ${favorites.has(community.id) ? 'fill-red-500 text-red-500' : 'text-white/50'}`}
                      />
                    </motion.button>
                  </div>

                  <p className="text-xs sm:text-sm text-white/70 mt-1 line-clamp-2">
                    {community.description}
                  </p>

                  <div className="mt-3 flex items-center justify-between">
                    <div className="flex items-center gap-4 text-xs sm:text-sm text-white/50">
                      <div className="flex items-center gap-1">
                        <Users className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span>{community.members} members</span>
                      </div>
                      <Badge variant="outline" className="border-white/20 text-white/60 text-xs">
                        {community.category}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Community Actions */}
              <div className={`flex items-center gap-2 ${viewMode === 'list' ? 'flex-shrink-0' : 'w-full'}`}>
                <motion.button
                  className="flex-1 sm:flex-none px-3 py-2 bg-purple-600/20 hover:bg-purple-600/30 rounded-lg text-xs sm:text-sm font-medium transition-colors"
                  whileTap={{ scale: 0.98 }}
                  {...createTouchHandler(() => handleJoinCommunity({} as React.MouseEvent, community))}
                >
                  {community.externalLink ? 'Join Now' : 'Join Community'}
                </motion.button>

                <motion.button
                  onClick={(e) => {
                    e.stopPropagation();
                    simulateHapticFeedback('light');
                    toast.success('Shared community!');
                  }}
                  className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
                  whileTap={{ scale: 0.95 }}
                >
                  <Share2 className="w-4 h-4 text-white/70" />
                </motion.button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}

      {filteredCommunities.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="col-span-full text-center py-12"
        >
          <IoPeopleOutline className="w-12 h-12 mx-auto text-white/40 mb-4" />
          <h3 className="text-lg font-medium text-white/60 mb-2">No communities found</h3>
          <p className="text-white/40 text-sm">
            {searchQuery ? `No communities match "${searchQuery}"` : 'No communities available at the moment'}
          </p>
        </motion.div>
      )}
    </div>
  );
};

// Resources Section Component with Real Database Integration
const ResourcesSection = () => {
  const [resources, setResources] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTip, setSelectedTip] = useState<any>(null);
  const [selectedGuide, setSelectedGuide] = useState<any>(null);
  const [selectedFaq, setSelectedFaq] = useState<any>(null);

  useEffect(() => {
    fetchResources();
  }, []);

  const fetchResources = async () => {
    try {
      setLoading(true);

      // Fetch featured and recent published content
      const [tipsResponse, guidesResponse, faqsResponse] = await Promise.all([
        supabase.from('tips').select('*').eq('status', 'published').order('is_featured', { ascending: false }).limit(2),
        supabase.from('guides').select('*').eq('status', 'published').order('is_featured', { ascending: false }).limit(2),
        supabase.from('faqs').select('*').eq('status', 'published').order('is_featured', { ascending: false }).limit(2)
      ]);

      const allResources = [
        ...(tipsResponse.data || []).map(item => ({ ...item, type: 'tip' })),
        ...(guidesResponse.data || []).map(item => ({ ...item, type: 'guide' })),
        ...(faqsResponse.data || []).map(item => ({ ...item, type: 'faq' }))
      ];

      setResources(allResources);
    } catch (error) {
      console.error('Error fetching resources:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleResourceClick = (resource: any) => {
    if (resource.type === 'tip') {
      setSelectedTip(resource);
    } else if (resource.type === 'guide') {
      setSelectedGuide(resource);
    } else if (resource.type === 'faq') {
      setSelectedFaq(resource);
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'tip': return '💡';
      case 'guide': return '📖';
      case 'faq': return '❓';
      default: return '📄';
    }
  };

  const getResourceTypeLabel = (type: string) => {
    switch (type) {
      case 'tip': return 'Tip';
      case 'guide': return 'Guide';
      case 'faq': return 'FAQ';
      default: return 'Resource';
    }
  };

  if (loading) {
    return (
      <div className="text-center py-8 text-white/60">
        Loading resources...
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-wrap gap-3 mb-4">
          <Badge className="bg-purple-700/50 hover:bg-purple-700 text-white cursor-pointer">
            Tips
          </Badge>
          <Badge className="bg-purple-700/50 hover:bg-purple-700 text-white cursor-pointer">
            Guides
          </Badge>
          <Badge className="bg-purple-700/50 hover:bg-purple-700 text-white cursor-pointer">
            FAQs
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/resources', '_blank')}
            className="text-white border-white/20 hover:bg-white/10"
          >
            View All Resources
          </Button>
        </div>

        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {resources.map((resource) => (
            <Card
              key={`${resource.type}-${resource.id}`}
              className="bg-white/10 backdrop-blur-md border border-white/20 text-white cursor-pointer hover:bg-white/15 transition-colors"
              onClick={() => handleResourceClick(resource)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg">
                    {resource.title || resource.question}
                  </CardTitle>
                  {resource.is_featured && (
                    <Badge className="bg-yellow-500/20 text-yellow-300">
                      Featured
                    </Badge>
                  )}
                </div>
                <Badge className="bg-purple-700/50 text-white w-fit">
                  {getResourceIcon(resource.type)} {getResourceTypeLabel(resource.type)}
                </Badge>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-white/70 mb-3 line-clamp-2">
                  {resource.description || resource.answer || resource.content?.substring(0, 100) + '...'}
                </p>
                <div className="flex items-center justify-between text-xs text-white/50">
                  <span>{resource.view_count || 0} views</span>
                  <span>Click to read more</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {resources.length === 0 && (
          <div className="text-center py-8 text-white/60">
            No resources available yet. Check back soon!
          </div>
        )}
      </div>

      {/* Import and use the modal components */}
      {selectedTip && (
        <TipDetailsModal
          tip={selectedTip}
          isOpen={!!selectedTip}
          onClose={() => setSelectedTip(null)}
        />
      )}
      {selectedGuide && (
        <GuideDetailsModal
          guide={selectedGuide}
          isOpen={!!selectedGuide}
          onClose={() => setSelectedGuide(null)}
        />
      )}
      {selectedFaq && (
        <FAQDetailsModal
          faq={selectedFaq}
          isOpen={!!selectedFaq}
          onClose={() => setSelectedFaq(null)}
        />
      )}
    </>
  );
};

// Local Info Section Component with Real Database Integration
const LocalInfoSection = ({
  viewMode,
  searchQuery,
  favorites,
  onFavoriteToggle,
  isMobile
}: {
  viewMode: ViewMode;
  searchQuery: string;
  favorites: Set<string>;
  onFavoriteToggle: (id: string) => void;
  isMobile: boolean;
}) => {
  const [localInfo, setLocalInfo] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [selectedItem, setSelectedItem] = React.useState<any>(null);

  // Fetch real local info from database
  React.useEffect(() => {
    const fetchLocalInfo = async () => {
      try {
        const { data, error } = await supabase
          .from('local_info')
          .select('*')
          .eq('is_active', true)
          .order('is_featured', { ascending: false })
          .order('priority', { ascending: false })
          .order('category', { ascending: true });

        if (error) throw error;

        // Transform data to match expected format
        const transformedLocalInfo = data?.map(item => ({
          id: item.id,
          title: item.title,
          description: item.description,
          category: item.category.charAt(0).toUpperCase() + item.category.slice(1),
          link: item.link,
          featured: item.is_featured || false,
          priority: item.priority || 0
        })) || [];

        setLocalInfo(transformedLocalInfo);
      } catch (error) {
        console.error('Error fetching local info:', error);
        // Fallback to empty array on error
        setLocalInfo([]);
      } finally {
        setLoading(false);
      }
    };

    fetchLocalInfo();
  }, []);

  // Filter local info based on search query
  const filteredLocalInfo = localInfo.filter(item =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleItemClick = useCallback((item: any) => {
    simulateHapticFeedback('light');
    if (item.link) {
      window.open(item.link, '_blank', 'noopener,noreferrer');
      toast.success(`Opening ${item.title}...`);
    } else {
      setSelectedItem(item);
    }
  }, []);

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'transportation': return '🚌';
      case 'food': return '🍕';
      case 'accommodation': return '🏨';
      case 'safety': return '🚨';
      case 'weather': return '🌤️';
      case 'attractions': return '🎡';
      default: return '📍';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'transportation': return 'bg-blue-700/50';
      case 'food': return 'bg-orange-700/50';
      case 'accommodation': return 'bg-green-700/50';
      case 'safety': return 'bg-red-700/50';
      case 'weather': return 'bg-cyan-700/50';
      case 'attractions': return 'bg-pink-700/50';
      default: return 'bg-purple-700/50';
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="bg-white/10 backdrop-blur-md border border-white/20 text-white h-full animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-white/20 rounded mb-2"></div>
              <div className="h-3 bg-white/10 rounded mb-4"></div>
              <div className="h-8 bg-white/10 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className={`grid gap-4 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
        {filteredLocalInfo.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="group"
          >
            <Card
              className="bg-white/10 backdrop-blur-md border border-white/20 text-white h-full cursor-pointer hover:bg-white/20 transition-all duration-300 overflow-hidden"
              {...createTouchHandler(() => handleItemClick(item))}
            >
              <CardContent className={`p-4 ${viewMode === 'list' ? 'flex items-center gap-4' : 'flex flex-col gap-4'}`}>
                {/* Local Info Header */}
                <div className={`flex items-start justify-between ${viewMode === 'list' ? 'flex-shrink-0' : 'w-full'}`}>
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{getCategoryIcon(item.category)}</span>
                    {item.featured && (
                      <Badge className="bg-yellow-500/20 text-yellow-300 text-xs">
                        Featured
                      </Badge>
                    )}
                  </div>
                  <motion.button
                    onClick={(e) => {
                      e.stopPropagation();
                      onFavoriteToggle(item.id);
                    }}
                    className="p-1 hover:bg-white/10 rounded-full transition-colors"
                    whileTap={{ scale: 0.9 }}
                  >
                    <Heart
                      className={`w-4 h-4 ${favorites.has(item.id) ? 'fill-red-500 text-red-500' : 'text-white/50'}`}
                    />
                  </motion.button>
                </div>

                {/* Local Info Content */}
                <div className={`${viewMode === 'list' ? 'flex-1' : 'w-full'}`}>
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                  </div>

                  <Badge className={`${getCategoryColor(item.category)} text-white mb-3`}>
                    {item.category}
                  </Badge>

                  <p className="text-sm text-white/70 mb-3 line-clamp-2">
                    {item.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-white/50">
                      {item.link ? 'Click to visit link' : 'Click for details'}
                    </span>
                    {item.link && (
                      <Badge variant="outline" className="border-white/20 text-white/60 text-xs">
                        External Link
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}

        {filteredLocalInfo.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="col-span-full text-center py-12"
          >
            <IoInformationCircleOutline className="w-12 h-12 mx-auto text-white/40 mb-4" />
            <h3 className="text-lg font-medium text-white/60 mb-2">No local info found</h3>
            <p className="text-white/40 text-sm">
              {searchQuery ? `No local info matches "${searchQuery}"` : 'No local information available at the moment'}
            </p>
          </motion.div>
        )}
      </div>

      {/* Local Info Detail Modal */}
      <ResponsiveModal
        isOpen={!!selectedItem}
        onClose={() => setSelectedItem(null)}
        size="md"
        showCloseButton={false}
        className="bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"
      >
        {selectedItem && (
          <div className="p-6 text-white">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">{getCategoryIcon(selectedItem.category)}</span>
              <h3 className="text-xl font-bold">{selectedItem.title}</h3>
            </div>

            <Badge className={`${getCategoryColor(selectedItem.category)} text-white mb-4`}>
              {selectedItem.category}
            </Badge>

            <p className="text-white/80 mb-6">{selectedItem.description}</p>

            <div className="flex gap-2">
              {selectedItem.link && (
                <Button
                  onClick={() => {
                    window.open(selectedItem.link, '_blank', 'noopener,noreferrer');
                    setSelectedItem(null);
                  }}
                  className="bg-purple-700 hover:bg-purple-600"
                >
                  Visit Link
                </Button>
              )}
              <Button
                variant="outline"
                onClick={() => setSelectedItem(null)}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </ResponsiveModal>
    </>
  );
};

// Error fallback component for FamHub page
const FamHubErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
    <div className="container mx-auto py-8 px-4">
      <Card className="p-6 bg-white/10 backdrop-blur-md border border-white/20 text-white">
        <CardContent className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-red-400 mb-4" />
          <h2 className="text-2xl font-bold mb-2">Unable to Load FamHub</h2>
          <p className="text-white/70 mb-6">
            We're having trouble loading the community features. This might be a temporary issue.
          </p>
          <div className="bg-red-500/20 rounded-md p-4 mb-6">
            <p className="text-sm text-red-300">{error.message}</p>
          </div>
          <Button
            onClick={resetErrorBoundary}
            className="bg-purple-700 hover:bg-purple-600"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    </div>
  </div>
);

const FamHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState<Tab>('CHAT');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  // Mobile-specific state
  const [isMobile, setIsMobile] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => setIsMobile(isMobileViewport());
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Enhanced tab change with haptic feedback
  const handleTabChange = useCallback((newTab: Tab) => {
    setActiveTab(newTab);
    simulateHapticFeedback('light');
  }, []);

  // View mode toggle with haptic feedback
  const handleViewModeToggle = useCallback(() => {
    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
    simulateHapticFeedback('light');
  }, []);

  // Pull-to-refresh functionality
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    simulateHapticFeedback('medium');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('Content refreshed!');
    } catch (error) {
      console.error('Refresh failed:', error);
      toast.error('Failed to refresh content');
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing]);

  // Favorite toggle with haptic feedback
  const handleFavoriteToggle = useCallback((itemId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(itemId)) {
        newFavorites.delete(itemId);
        toast.success('Removed from favorites');
      } else {
        newFavorites.add(itemId);
        toast.success('Added to favorites');
      }
      simulateHapticFeedback('light');
      return newFavorites;
    });
  }, []);

  return (
    <ErrorBoundary FallbackComponent={FamHubErrorFallback}>
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
        {/* Enhanced Mobile-First Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="container mx-auto py-4 sm:py-8 px-4"
        >
          <Card className="bg-white/10 backdrop-blur-md border border-white/20 text-white rounded-xl sm:rounded-2xl overflow-hidden">
            {/* Enhanced Mobile-First Header */}
            <CardHeader className="pb-4 sm:pb-6">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-lg flex items-center justify-center">
                    <IoPeopleOutline className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <CardTitle className="text-xl sm:text-2xl font-bold">FamHub</CardTitle>
                    <p className="text-white/70 text-sm">Connect with your festival family</p>
                  </div>
                </div>

                {/* Mobile-Optimized Search and Refresh */}
                <div className="flex items-center gap-2">
                  <div className="relative flex-1 sm:w-64">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
                    <input
                      type="text"
                      placeholder={isMobile ? "Search..." : "Search communities..."}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 focus:border-purple-400 focus:outline-none transition-colors"
                      style={{ fontSize: '16px' }} // Prevents zoom on iOS
                    />
                  </div>

                  <motion.button
                    disabled={isRefreshing}
                    className="p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-colors disabled:opacity-50"
                    whileTap={{ scale: 0.95 }}
                    {...createTouchHandler(handleRefresh)}
                  >
                    <motion.div
                      animate={isRefreshing ? { rotate: 360 } : {}}
                      transition={isRefreshing ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
                    >
                      <RefreshCw className="w-4 h-4 text-white/70" />
                    </motion.div>
                  </motion.button>
                </div>
              </motion.div>
            </CardHeader>

            <CardContent className="px-4 sm:px-6 pb-6">
              {/* Enhanced Mobile-First Tabs */}
              <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as Tab)} className="w-full">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6"
                >
                  {/* Mobile-Optimized Tab Navigation */}
                  <TabsList className="grid grid-cols-2 sm:grid-cols-4 bg-white/10 border border-white/20 p-1 rounded-lg w-full sm:w-auto">
                    {filterTabs.map(({ id, label, icon: Icon }) => (
                      <TabsTrigger
                        key={id}
                        value={id}
                        className="data-[state=active]:bg-purple-700 data-[state=active]:text-white text-white/70 hover:text-white rounded-md flex items-center justify-center gap-2 min-h-[44px] transition-all duration-300"
                      >
                        <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                        <span className={isMobile ? "text-xs" : "text-sm"}>{isMobile ? label.split(' ')[0] : label}</span>
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  {/* Mobile-Optimized View Controls */}
                  <div className="flex items-center gap-2">
                    <motion.button
                      className="flex items-center gap-2 px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-colors text-sm"
                      whileTap={{ scale: 0.95 }}
                      {...createTouchHandler(() => setShowFilters(!showFilters))}
                    >
                      <Filter className="w-4 h-4" />
                      {!isMobile && <span>Filters</span>}
                    </motion.button>

                    <motion.button
                      aria-label={viewMode === 'grid' ? 'Switch to list view' : 'Switch to grid view'}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-colors"
                      whileTap={{ scale: 0.95 }}
                      {...createTouchHandler(handleViewModeToggle)}
                    >
                      {viewMode === 'grid' ? <IoList className="w-4 h-4" /> : <IoGrid className="w-4 h-4" />}
                    </motion.button>
                  </div>
                </motion.div>

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <TabsContent value="CHAT">
                    <ChatSection />
                  </TabsContent>

                  <TabsContent value="COMMUNITIES">
                    <CommunitiesSection
                      viewMode={viewMode}
                      searchQuery={searchQuery}
                      favorites={favorites}
                      onFavoriteToggle={handleFavoriteToggle}
                      isMobile={isMobile}
                    />
                  </TabsContent>

                  <TabsContent value="RESOURCES">
                    <ResourcesSection />
                  </TabsContent>

                  <TabsContent value="LOCAL_INFO">
                    <LocalInfoSection
                      viewMode={viewMode}
                      searchQuery={searchQuery}
                      favorites={favorites}
                      onFavoriteToggle={handleFavoriteToggle}
                      isMobile={isMobile}
                    />
                  </TabsContent>
                </motion.div>
              </AnimatePresence>
              </Tabs>
            </CardContent>
          </Card>

          {/* Mobile-safe bottom spacing */}
          <div className="h-4 sm:h-8" />
        </motion.div>

        {/* Development-only Mobile UX Testing Tool */}
        <MobileUXTester />
      </div>
    </ErrorBoundary>
  );
};

export default FamHub;
