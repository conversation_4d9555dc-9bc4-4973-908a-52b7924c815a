import React from 'react';
import { Badge } from '@/components/ui/badge';

// Modal Content Components for proper hierarchy (eliminates duplicate titles)

interface ModalContentProps {
  children: React.ReactNode;
  className?: string;
  noPadding?: boolean;
}

export const ModalContent: React.FC<ModalContentProps> = ({ 
  children, 
  className = '', 
  noPadding = false 
}) => {
  return (
    <div className={`
      ${noPadding ? '' : 'p-4 sm:p-6'} 
      text-white 
      ${className}
    `}>
      {children}
    </div>
  );
};

interface ModalSectionProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
  spacing?: 'tight' | 'normal' | 'loose';
}

export const ModalSection: React.FC<ModalSectionProps> = ({ 
  title, 
  children, 
  className = '',
  spacing = 'normal'
}) => {
  const spacingClasses = {
    tight: 'space-y-2',
    normal: 'space-y-3 sm:space-y-4',
    loose: 'space-y-4 sm:space-y-6',
  };

  return (
    <div className={`${spacingClasses[spacing]} ${className}`}>
      {title && (
        <h3 className="text-lg font-medium text-white/90 border-b border-white/10 pb-2">
          {title}
        </h3>
      )}
      {children}
    </div>
  );
};

interface ModalMetadataProps {
  items: Array<{
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    value: string;
  }>;
  className?: string;
}

export const ModalMetadata: React.FC<ModalMetadataProps> = ({ 
  items, 
  className = '' 
}) => {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 gap-3 ${className}`}>
      {items.map((item, index) => {
        const IconComponent = item.icon;
        return (
          <div key={`${item.label}-${index}`} className="flex items-center gap-2 text-sm text-white/70">
            <IconComponent className="w-4 h-4 flex-shrink-0" />
            <span className="font-medium">{item.label}:</span>
            <span className="truncate">{item.value}</span>
          </div>
        );
      })}
    </div>
  );
};

interface ModalDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export const ModalDescription: React.FC<ModalDescriptionProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`text-white/80 leading-relaxed ${className}`}>
      {children}
    </div>
  );
};

interface ModalTagsProps {
  tags: string[];
  variant?: 'default' | 'category' | 'featured';
  className?: string;
}

export const ModalTags: React.FC<ModalTagsProps> = ({ 
  tags, 
  variant = 'default',
  className = '' 
}) => {
  const getVariantStyles = (variant: string) => {
    switch (variant) {
      case 'category':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'featured':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      default:
        return 'bg-white/10 text-white/80 border-white/20';
    }
  };

  if (!tags || tags.length === 0) return null;

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {tags.map((tag, index) => (
        <Badge
          key={`${tag}-${index}`}
          className={`text-xs ${getVariantStyles(variant)}`}
        >
          {tag}
        </Badge>
      ))}
    </div>
  );
};

interface ModalActionsProps {
  children: React.ReactNode;
  alignment?: 'left' | 'center' | 'right' | 'between';
  className?: string;
  spacing?: 'tight' | 'normal' | 'loose';
}

export const ModalActions: React.FC<ModalActionsProps> = ({ 
  children, 
  alignment = 'right',
  className = '',
  spacing = 'normal'
}) => {
  const alignmentClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between',
  };

  const spacingClasses = {
    tight: 'gap-2',
    normal: 'gap-3',
    loose: 'gap-4',
  };

  return (
    <div className={`
      flex flex-wrap items-center 
      ${alignmentClasses[alignment]} 
      ${spacingClasses[spacing]} 
      pt-4 border-t border-white/10
      ${className}
    `}>
      {children}
    </div>
  );
};

interface ModalImageProps {
  src?: string;
  alt: string;
  fallbackType?: 'event' | 'activity' | 'guide' | 'tip' | 'faq';
  className?: string;
}

export const ModalImage: React.FC<ModalImageProps> = ({ 
  src, 
  alt, 
  fallbackType = 'event',
  className = '' 
}) => {
  // Fallback gradient based on type
  const getFallbackGradient = (type: string) => {
    switch (type) {
      case 'activity':
        return 'from-green-500 to-blue-500';
      case 'guide':
        return 'from-purple-500 to-pink-500';
      case 'tip':
        return 'from-yellow-500 to-orange-500';
      case 'faq':
        return 'from-blue-500 to-indigo-500';
      default:
        return 'from-purple-500 to-pink-500';
    }
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {src ? (
        <img 
          src={src} 
          alt={alt}
          className="w-full h-full object-cover"
          loading="lazy"
        />
      ) : (
        <div className={`
          w-full h-full 
          bg-gradient-to-br ${getFallbackGradient(fallbackType)}
          flex items-center justify-center
        `}>
          <span className="text-white text-lg font-semibold text-center px-4">
            {alt}
          </span>
        </div>
      )}
    </div>
  );
};

interface ModalStatsProps {
  stats: Array<{
    label: string;
    value: string | number;
    icon?: React.ComponentType<{ className?: string }>;
  }>;
  className?: string;
}

export const ModalStats: React.FC<ModalStatsProps> = ({ 
  stats, 
  className = '' 
}) => {
  return (
    <div className={`grid grid-cols-2 sm:grid-cols-3 gap-4 ${className}`}>
      {stats.map((stat, index) => {
        const IconComponent = stat.icon;
        return (
          <div key={`${stat.label}-${index}`} className="text-center">
            <div className="flex items-center justify-center gap-1 text-white/60 text-xs mb-1">
              {IconComponent && <IconComponent className="w-3 h-3" />}
              <span>{stat.label}</span>
            </div>
            <div className="text-white font-semibold text-lg">
              {stat.value}
            </div>
          </div>
        );
      })}
    </div>
  );
};

// Responsive Modal Footer for consistent action layouts
interface ModalFooterProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'sticky';
}

export const ModalFooter: React.FC<ModalFooterProps> = ({ 
  children, 
  className = '',
  variant = 'default'
}) => {
  const baseClasses = "p-4 sm:p-6 border-t border-white/10 bg-black/20";
  const variantClasses = variant === 'sticky' 
    ? "sticky bottom-0 backdrop-blur-sm" 
    : "";

  return (
    <div className={`${baseClasses} ${variantClasses} ${className}`}>
      {children}
    </div>
  );
};

// All components are already exported above with individual export statements
