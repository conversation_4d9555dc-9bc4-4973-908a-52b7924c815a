/**
 * Smart Group Formation Service
 * 
 * Handles intelligent group suggestions and formation based on activity attendance,
 * music preferences, and real-time coordination. Enables authentic community building
 * through shared interests rather than superficial matching.
 * 
 * @module SmartGroupFormationService
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { BaseService, ServiceResponse } from './base-service'
import {
  validateGroupFormationRequest,
  validateGroupSuggestion,
  generateDefaultGroupName,
  generateDefaultGroupDescription,
  GROUP_FORMATION_LIMITS
} from '../activity-coordination/utils'
import type {
  GroupSuggestion,
  GroupSuggestionResponse,
  GroupFormationType,
  SuggestionStatus,
  SmartGroupFormationRequest,
  ActivityBasedGroupCandidate,
  MusicBasedGroupCandidate,
  GroupFormationInsights
} from '../activity-coordination/types'
import type { Profile, Group } from '@/types'

/**
 * Service for intelligent group formation and suggestions
 */
export class SmartGroupFormationService extends BaseService {
  
  /**
   * Create a smart group suggestion based on activity attendance
   */
  async createActivityBasedGroupSuggestion(
    creatorId: string,
    festivalId: string,
    activityFocus: string[],
    options: {
      suggestedName?: string
      suggestedDescription?: string
      minMembers?: number
      maxMembers?: number
      confidenceThreshold?: number
    } = {}
  ): Promise<ServiceResponse<GroupSuggestion>> {
    return this.handleResponse(
      (async () => {
        // Validate input parameters
        if (!creatorId || !festivalId || !activityFocus || activityFocus.length === 0) {
          throw new Error('Creator ID, festival ID, and activity focus are required')
        }

        const {
          suggestedName = generateDefaultGroupName('activity_based', activityFocus),
          suggestedDescription = generateDefaultGroupDescription('activity_based', activityFocus),
          minMembers = GROUP_FORMATION_LIMITS.MIN_MEMBERS,
          maxMembers = GROUP_FORMATION_LIMITS.MAX_MEMBERS,
          confidenceThreshold = GROUP_FORMATION_LIMITS.DEFAULT_CONFIDENCE_THRESHOLD
        } = options

        // Validate options
        if (minMembers < GROUP_FORMATION_LIMITS.MIN_MEMBERS || minMembers > maxMembers) {
          throw new Error(`Invalid member limits: min=${minMembers}, max=${maxMembers}`)
        }

        if (confidenceThreshold < 0 || confidenceThreshold > 1) {
          throw new Error('Confidence threshold must be between 0 and 1')
        }

        // Find users with similar activity interests
        const { data: candidates, error: candidatesError } = await this.client
          .rpc('find_activity_based_group_candidates', {
            target_user_id: creatorId,
            festival_id_param: festivalId,
            min_shared_activities: 2,
            result_limit: maxMembers
          })

        if (candidatesError) throw candidatesError

        // Filter candidates by confidence threshold
        const qualifiedCandidates = candidates?.filter(
          (candidate: any) => candidate.compatibility_score >= confidenceThreshold
        ) || []

        if (qualifiedCandidates.length < minMembers - 1) {
          throw new Error('Not enough qualified candidates found for group formation')
        }

        // Create target users array (creator + candidates)
        const targetUsers = [creatorId, ...qualifiedCandidates.map((c: any) => c.user_id)]

        // Create the suggestion
        const { data: suggestion, error: suggestionError } = await this.client
          .from('group_suggestions')
          .insert({
            suggested_name: suggestedName,
            suggested_description: suggestedDescription,
            formation_type: 'activity_based',
            festival_id: festivalId,
            activity_focus: activityFocus,
            target_users: targetUsers,
            creator_id: creatorId,
            min_members: minMembers,
            max_members: maxMembers,
            confidence_score: Math.max(...qualifiedCandidates.map((c: any) => c.compatibility_score))
          })
          .select()
          .single()

        if (suggestionError) throw suggestionError

        return suggestion as GroupSuggestion
      })()
    )
  }

  /**
   * Create a smart group suggestion based on music preferences
   */
  async createMusicBasedGroupSuggestion(
    creatorId: string,
    festivalId: string,
    musicFocus: string[],
    options: {
      suggestedName?: string
      suggestedDescription?: string
      minMembers?: number
      maxMembers?: number
      focusType?: 'artists' | 'genres'
    } = {}
  ): Promise<ServiceResponse<GroupSuggestion>> {
    return this.handleResponse(
      (async () => {
        const {
          suggestedName = `${musicFocus[0]} Fans`,
          suggestedDescription = `Connect with fellow ${musicFocus.join(', ')} enthusiasts`,
          minMembers = 3,
          maxMembers = 20,
          focusType = 'artists'
        } = options

        // Find users with similar music taste
        const { data: musicBuddies, error: buddiesError } = await this.client
          .rpc('find_music_buddies', {
            target_user_id: creatorId,
            result_limit: maxMembers
          })

        if (buddiesError) throw buddiesError

        // Filter by specific music focus
        const relevantBuddies = musicBuddies?.filter((buddy: any) => {
          const sharedItems = focusType === 'artists' ? buddy.shared_artists : buddy.shared_genres
          return musicFocus.some(focus => sharedItems >= 1) // At least one shared item
        }) || []

        if (relevantBuddies.length < minMembers - 1) {
          throw new Error('Not enough music buddies found for group formation')
        }

        // Create target users array
        const targetUsers = [creatorId, ...relevantBuddies.map((b: any) => b.user_id)]

        // Create the suggestion
        const { data: suggestion, error: suggestionError } = await this.client
          .from('group_suggestions')
          .insert({
            suggested_name: suggestedName,
            suggested_description: suggestedDescription,
            formation_type: 'music_based',
            festival_id: festivalId,
            music_focus: musicFocus,
            target_users: targetUsers,
            creator_id: creatorId,
            min_members: minMembers,
            max_members: maxMembers,
            confidence_score: Math.max(...relevantBuddies.map((b: any) => b.compatibility_score / 10)) // Normalize score
          })
          .select()
          .single()

        if (suggestionError) throw suggestionError

        return suggestion as GroupSuggestion
      })()
    )
  }

  /**
   * Create a hybrid group suggestion (activity + music based)
   */
  async createHybridGroupSuggestion(
    creatorId: string,
    festivalId: string,
    activityFocus: string[],
    musicFocus: string[],
    options: {
      suggestedName?: string
      suggestedDescription?: string
      minMembers?: number
      maxMembers?: number
    } = {}
  ): Promise<ServiceResponse<GroupSuggestion>> {
    return this.handleResponse(
      (async () => {
        const {
          suggestedName = `${activityFocus[0]} & ${musicFocus[0]} Group`,
          suggestedDescription = `Connect through ${activityFocus.join(', ')} and ${musicFocus.join(', ')}`,
          minMembers = 3,
          maxMembers = 20
        } = options

        // Get both activity and music candidates
        const [activityCandidates, musicCandidates] = await Promise.all([
          this.client.rpc('find_activity_based_group_candidates', {
            target_user_id: creatorId,
            festival_id_param: festivalId,
            min_shared_activities: 1,
            result_limit: maxMembers * 2
          }),
          this.client.rpc('find_music_buddies', {
            target_user_id: creatorId,
            result_limit: maxMembers * 2
          })
        ])

        if (activityCandidates.error) throw activityCandidates.error
        if (musicCandidates.error) throw musicCandidates.error

        // Find users who appear in both lists (hybrid candidates)
        const activityUserIds = new Set(activityCandidates.data?.map((c: any) => c.user_id) || [])
        const musicUserIds = new Set(musicCandidates.data?.map((c: any) => c.user_id) || [])
        
        const hybridUserIds = Array.from(activityUserIds).filter(id => musicUserIds.has(id))

        if (hybridUserIds.length < minMembers - 1) {
          throw new Error('Not enough hybrid candidates found for group formation')
        }

        // Create target users array
        const targetUsers = [creatorId, ...hybridUserIds.slice(0, maxMembers - 1)]

        // Calculate hybrid confidence score
        const avgActivityScore = activityCandidates.data
          ?.filter((c: any) => hybridUserIds.includes(c.user_id))
          .reduce((sum: number, c: any) => sum + c.compatibility_score, 0) / hybridUserIds.length || 0

        const avgMusicScore = musicCandidates.data
          ?.filter((c: any) => hybridUserIds.includes(c.user_id))
          .reduce((sum: number, c: any) => sum + c.compatibility_score, 0) / hybridUserIds.length || 0

        const hybridScore = (avgActivityScore + avgMusicScore / 10) / 2 // Normalize and average

        // Create the suggestion
        const { data: suggestion, error: suggestionError } = await this.client
          .from('group_suggestions')
          .insert({
            suggested_name: suggestedName,
            suggested_description: suggestedDescription,
            formation_type: 'hybrid',
            festival_id: festivalId,
            activity_focus: activityFocus,
            music_focus: musicFocus,
            target_users: targetUsers,
            creator_id: creatorId,
            min_members: minMembers,
            max_members: maxMembers,
            confidence_score: hybridScore
          })
          .select()
          .single()

        if (suggestionError) throw suggestionError

        return suggestion as GroupSuggestion
      })()
    )
  }

  /**
   * Get group suggestions for a user
   */
  async getUserGroupSuggestions(
    userId: string,
    festivalId?: string,
    status: SuggestionStatus = 'pending'
  ): Promise<ServiceResponse<GroupSuggestion[]>> {
    return this.handleResponse(
      (async () => {
        let query = this.client
          .from('group_suggestions')
          .select('*')
          .contains('target_users', [userId])
          .eq('status', status)
          .gt('expires_at', new Date().toISOString())
          .order('confidence_score', { ascending: false })

        if (festivalId) {
          query = query.eq('festival_id', festivalId)
        }

        const { data, error } = await query

        if (error) throw error

        return data as GroupSuggestion[]
      })()
    )
  }

  /**
   * Respond to a group suggestion
   */
  async respondToGroupSuggestion(
    suggestionId: string,
    userId: string,
    response: boolean,
    notes?: string
  ): Promise<ServiceResponse<GroupSuggestionResponse>> {
    return this.handleResponse(
      (async () => {
        // Record the response
        const { data: responseData, error: responseError } = await this.client
          .from('group_suggestion_responses')
          .upsert({
            suggestion_id: suggestionId,
            user_id: userId,
            response,
            response_notes: notes,
            responded_at: new Date().toISOString()
          })
          .select()
          .single()

        if (responseError) throw responseError

        // Check if we should auto-form the group
        if (response) {
          const { data: groupId, error: formationError } = await this.client
            .rpc('check_and_form_group_from_suggestion', {
              suggestion_id_param: suggestionId
            })

          // Don't throw error if group formation fails - just log it
          if (formationError) {
            console.warn('Group auto-formation failed:', formationError)
          }

          // If group was formed, add the group_id to the response
          if (groupId) {
            (responseData as any).formed_group_id = groupId
          }
        }

        return responseData as GroupSuggestionResponse
      })()
    )
  }

  /**
   * Get suggestion responses for a suggestion
   */
  async getSuggestionResponses(
    suggestionId: string
  ): Promise<ServiceResponse<GroupSuggestionResponse[]>> {
    return this.handleResponse(
      this.client
        .from('group_suggestion_responses')
        .select(`
          *,
          profiles!group_suggestion_responses_user_id_fkey(
            id, username, full_name, avatar_url
          )
        `)
        .eq('suggestion_id', suggestionId)
        .order('responded_at', { ascending: false })
    )
  }

  /**
   * Create a spontaneous group for immediate coordination
   */
  async createSpontaneousGroup(
    creatorId: string,
    name: string,
    description: string,
    festivalId: string,
    options: {
      activityFocus?: string[]
      musicFocus?: string[]
      maxMembers?: number
      expiresInHours?: number
    } = {}
  ): Promise<ServiceResponse<Group>> {
    return this.handleResponse(
      (async () => {
        const {
          activityFocus = [],
          musicFocus = [],
          maxMembers = 10,
          expiresInHours = 6
        } = options

        const expiresAt = new Date()
        expiresAt.setHours(expiresAt.getHours() + expiresInHours)

        // Create the group directly (no suggestion phase for spontaneous groups)
        const { data: group, error: groupError } = await this.client
          .from('groups')
          .insert({
            name,
            description,
            festival_id: festivalId,
            creator_id: creatorId,
            formation_type: 'spontaneous',
            max_members: maxMembers,
            activity_focus: activityFocus,
            music_focus: musicFocus,
            is_active: true,
            expires_at: expiresAt.toISOString()
          })
          .select()
          .single()

        if (groupError) throw groupError

        // Add creator as admin member
        const { error: memberError } = await this.client
          .from('group_members')
          .insert({
            group_id: group.id,
            user_id: creatorId,
            role: 'admin',
            joined_at: new Date().toISOString()
          })

        if (memberError) throw memberError

        return group as Group
      })()
    )
  }

  /**
   * Get group formation insights for a user
   */
  async getGroupFormationInsights(
    userId: string,
    festivalId: string
  ): Promise<ServiceResponse<GroupFormationInsights>> {
    return this.handleResponse(
      (async () => {
        // Get user's activity attendance
        const { data: userActivities, error: activitiesError } = await this.client
          .from('activity_attendance')
          .select(`
            activity_id,
            status,
            activities!activity_attendance_activity_id_fkey(title, type)
          `)
          .eq('user_id', userId)
          .in('status', ['going', 'interested'])

        if (activitiesError) throw activitiesError

        // Get user's music preferences
        const { data: musicPrefs, error: musicError } = await this.client
          .from('artist_preferences')
          .select('artist_name, genre')
          .eq('user_id', userId)
          .in('preference_level', ['love', 'like'])

        if (musicError) throw musicError

        // Get existing group suggestions
        const { data: suggestions, error: suggestionsError } = await this.getUserGroupSuggestions(
          userId, 
          festivalId, 
          'pending'
        )

        if (suggestionsError) throw suggestionsError

        // Calculate insights
        const insights: GroupFormationInsights = {
          user_id: userId,
          festival_id: festivalId,
          activity_interests: userActivities?.map(a => (a as any).activities?.title).filter(Boolean) || [],
          music_interests: musicPrefs?.map(p => p.artist_name).filter(Boolean) || [],
          pending_suggestions: suggestions?.length || 0,
          formation_potential: this.calculateFormationPotential(userActivities || [], musicPrefs || []),
          recommended_actions: this.generateRecommendedActions(userActivities || [], musicPrefs || [], suggestions || [])
        }

        return insights
      })()
    )
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private calculateFormationPotential(activities: any[], musicPrefs: any[]): number {
    // Simple scoring algorithm - more sophisticated in production
    const activityScore = Math.min(activities.length * 0.2, 1.0)
    const musicScore = Math.min(musicPrefs.length * 0.1, 0.5)
    return Math.min(activityScore + musicScore, 1.0)
  }

  private generateRecommendedActions(activities: any[], musicPrefs: any[], suggestions: any[]): string[] {
    const actions: string[] = []

    if (activities.length < 3) {
      actions.push('Mark attendance for more activities to improve group matching')
    }

    if (musicPrefs.length < 5) {
      actions.push('Add more music preferences to find like-minded festival-goers')
    }

    if (suggestions.length === 0) {
      actions.push('Create a group suggestion based on your interests')
    }

    if (suggestions.length > 0) {
      actions.push('Respond to pending group suggestions')
    }

    return actions
  }
}

// Export singleton instance
export const smartGroupFormationService = new SmartGroupFormationService()
