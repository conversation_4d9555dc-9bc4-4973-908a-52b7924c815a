import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { useProfile } from '../../hooks/useProfile';
import { isAdminRole } from '@/lib/utils/auth';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { MoreVertical, Pencil, Trash, HelpCircle, Plus } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface FAQ {
  id: number; // FAQ table uses integer IDs
  question: string;
  answer: string;
  category: string | null;
  is_active: boolean;
  order_index: number;
  created_at: string;
  updated_at: string | null;
}

const categoryColors = {
  GENERAL: 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30',
  TICKETS: 'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30',
  ACCOMMODATION: 'bg-green-500/20 text-green-400 hover:bg-green-500/30',
  TRANSPORTATION: 'bg-purple-500/20 text-purple-400 hover:bg-purple-500/30',
  FESTIVAL: 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30',
  SAFETY: 'bg-red-500/20 text-red-400 hover:bg-red-500/30',
};

const AdminFAQ: React.FC = () => {
  const navigate = useNavigate();
  const { profile, loading: profileLoading } = useProfile();
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingFaq, setEditingFaq] = useState<FAQ | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newFaq, setNewFaq] = useState({
    question: '',
    answer: '',
    category: 'GENERAL' as keyof typeof categoryColors,
    order_index: 0
  });

  // Redirect non-admin users
  useEffect(() => {
    if (!profileLoading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [profileLoading, profile, navigate]);

  // Fetch FAQs
  const fetchFaqs = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('faqs')
        .select('*')
        .order('category', { ascending: true })
        .order('created_at', { ascending: false }); // Use created_at since order_index might not exist

      if (error) throw error;
      // Transform the data to match our interface
      const transformedData = (data || []).map(faq => ({
        ...faq,
        id: faq.id, // ID is integer from database
        category: faq.category || 'GENERAL', // Use category from database or default
        is_active: faq.is_active ?? true, // Use is_active from database
        order_index: faq.order_index ?? 0, // Use order_index from database or default
        updated_at: faq.updated_at || faq.created_at || new Date().toISOString()
      }));
      setFaqs(transformedData);
    } catch (error) {
      console.error('Error fetching FAQs:', error);
      setError('Failed to load FAQs');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!profileLoading && isAdminRole(profile?.role)) {
      fetchFaqs();
    }
  }, [profileLoading, profile]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const faqData = {
        question: newFaq.question,
        answer: newFaq.answer,
        category: newFaq.category,
        order_index: newFaq.order_index,
        is_active: true
      };

      const { error } = await supabase
        .from('faqs')
        .insert([faqData]);

      if (error) throw error;

      toast.success('FAQ created successfully!');
      resetForm();
      fetchFaqs();
    } catch (error) {
      console.error('Error creating FAQ:', error);
      toast.error('Failed to create FAQ');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingFaq) return;

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('faqs')
        .update({
          question: newFaq.question,
          answer: newFaq.answer,
          category: newFaq.category,
          order_index: newFaq.order_index,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingFaq.id);

      if (error) throw error;

      toast.success('FAQ updated successfully!');
      resetForm();
      fetchFaqs();
    } catch (error) {
      console.error('Error updating FAQ:', error);
      toast.error('Failed to update FAQ');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (faq: FAQ) => {
    setEditingFaq(faq);
    setNewFaq({
      question: faq.question,
      answer: faq.answer,
      category: faq.category as keyof typeof categoryColors,
      order_index: faq.order_index
    });
    setShowCreateForm(true);
  };

  const handleDelete = async (faqId: string) => {
    if (!confirm('Are you sure you want to delete this FAQ? This action cannot be undone.')) return;

    try {
      const { error } = await supabase
        .from('faqs')
        .delete()
        .eq('id', faqId);

      if (error) throw error;

      toast.success('FAQ deleted successfully!');
      fetchFaqs();
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      toast.error('Failed to delete FAQ');
    }
  };

  const toggleActive = async (faq: FAQ) => {
    try {
      const { error } = await supabase
        .from('faqs')
        .update({ is_active: !faq.is_active })
        .eq('id', faq.id);

      if (error) throw error;

      toast.success(`FAQ ${faq.is_active ? 'deactivated' : 'activated'} successfully!`);
      fetchFaqs();
    } catch (error) {
      console.error('Error toggling FAQ status:', error);
      toast.error('Failed to update FAQ status');
    }
  };

  const resetForm = () => {
    setEditingFaq(null);
    setShowCreateForm(false);
    setNewFaq({
      question: '',
      answer: '',
      category: 'GENERAL' as keyof typeof categoryColors,
      order_index: 0
    });
  };

  if (profileLoading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">FAQ Management</h1>
          <p className="text-muted-foreground mt-2">
            Manage frequently asked questions
          </p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-primary/20 hover:bg-primary/30"
        >
          <Plus className="mr-2 h-4 w-4" />
          Create FAQ
        </Button>
      </div>

      {error && (
        <div className="rounded-lg border border-red-500/20 p-4 text-red-400 bg-red-500/10">
          {error}
        </div>
      )}

      {/* Create/Edit FAQ Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingFaq ? 'Edit FAQ' : 'Create New FAQ'}</CardTitle>
            <CardDescription>
              {editingFaq ? 'Update the FAQ details' : 'Add a new frequently asked question'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={editingFaq ? handleUpdate : handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Category</label>
                  <select
                    value={newFaq.category}
                    onChange={(e) => setNewFaq(prev => ({ ...prev, category: e.target.value as keyof typeof categoryColors }))}
                    className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="GENERAL">General</option>
                    <option value="TICKETS">Tickets</option>
                    <option value="ACCOMMODATION">Accommodation</option>
                    <option value="TRANSPORTATION">Transportation</option>
                    <option value="FESTIVAL">Festival</option>
                    <option value="SAFETY">Safety</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Order Index</label>
                  <input
                    type="number"
                    min="0"
                    value={newFaq.order_index}
                    onChange={(e) => setNewFaq(prev => ({ ...prev, order_index: parseInt(e.target.value) || 0 }))}
                    className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Question *</label>
                <input
                  type="text"
                  placeholder="Enter the question"
                  value={newFaq.question}
                  onChange={(e) => setNewFaq(prev => ({ ...prev, question: e.target.value }))}
                  className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Answer *</label>
                <textarea
                  placeholder="Enter the answer"
                  value={newFaq.answer}
                  onChange={(e) => setNewFaq(prev => ({ ...prev, answer: e.target.value }))}
                  className="w-full px-4 py-2 bg-white/5 border border-white/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-32"
                  required
                />
              </div>

              <div className="flex gap-2">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-purple-500/20 hover:bg-purple-500/30"
                >
                  {isSubmitting ? (editingFaq ? 'Updating...' : 'Creating...') : (editingFaq ? 'Update FAQ' : 'Create FAQ')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetForm}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* FAQs List */}
      {loading ? (
        <div className="text-center py-8 text-muted-foreground">
          Loading FAQs...
        </div>
      ) : faqs.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No FAQs found</CardTitle>
            <CardDescription>
              Get started by creating your first FAQ
            </CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <div className="grid gap-6">
          {faqs.map((faq) => (
            <Card key={faq.id} className={!faq.is_active ? 'opacity-60' : ''}>
              <CardHeader className="flex flex-row items-start justify-between space-y-0">
                <div className="space-y-1 flex-1">
                  <CardTitle className="flex items-center gap-2">
                    <HelpCircle className="h-5 w-5" />
                    {faq.question}
                  </CardTitle>
                  <CardDescription>{faq.answer}</CardDescription>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEdit(faq)}>
                      <Pencil className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => toggleActive(faq)}>
                      {faq.is_active ? 'Deactivate' : 'Activate'}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-red-400"
                      onClick={() => handleDelete(faq.id)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <Badge
                    variant="secondary"
                    className={cn(categoryColors[faq.category as keyof typeof categoryColors] || categoryColors.GENERAL)}
                  >
                    {faq.category}
                  </Badge>
                  <div className="text-sm text-muted-foreground">
                    Order: {faq.order_index}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {faq.is_active ? 'Active' : 'Inactive'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Updated {format(new Date(faq.updated_at || faq.created_at), 'PPP')}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdminFAQ;
