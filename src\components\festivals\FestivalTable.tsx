import React from 'react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON>cil, Trash2, Eye } from 'lucide-react';
import { Database } from '@/types/supabase';

type Festival = Database['public']['Tables']['festivals']['Row'];

interface FestivalTableProps {
  festivals: Festival[];
  onEdit?: (festival: Festival) => void;
  onDelete?: (festivalId: string) => void;
  onView?: (festival: Festival) => void;
  isLoading?: boolean;
}

export function FestivalTable({
  festivals,
  onEdit,
  onDelete,
  onView,
  isLoading = false,
}: FestivalTableProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Loading festivals...</div>
      </div>
    );
  }

  if (festivals.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">No festivals found</div>
      </div>
    );
  }

  const getStatusBadge = (festival: Festival) => {
    const now = new Date();
    const startDate = festival.start_date ? new Date(festival.start_date) : null;
    const endDate = festival.end_date ? new Date(festival.end_date) : null;

    if (!startDate) {
      return <Badge variant="outline">Unknown</Badge>;
    }

    if (now < startDate) {
      return <Badge variant="secondary">Upcoming</Badge>;
    } else if (endDate && now >= startDate && now <= endDate) {
      return <Badge variant="default">Active</Badge>;
    } else {
      return <Badge variant="outline">Past</Badge>;
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Start Date</TableHead>
            <TableHead>End Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {festivals.map((festival) => (
            <TableRow key={festival.id}>
              <TableCell className="font-medium">
                {festival.name}
              </TableCell>
              <TableCell>
                {festival.location || 'No location specified'}
              </TableCell>
              <TableCell>
                {festival.start_date ? format(new Date(festival.start_date), 'MMM dd, yyyy') : 'No date'}
              </TableCell>
              <TableCell>
                {festival.end_date ? format(new Date(festival.end_date), 'MMM dd, yyyy') : 'No date'}
              </TableCell>
              <TableCell>
                {getStatusBadge(festival)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-2">
                  {onView && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onView(festival)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  {onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(festival)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(festival.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
