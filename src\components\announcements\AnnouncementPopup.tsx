import React from 'react';
import { Megaphone, AlertTriangle, Info, CheckCircle, Tag, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';
import { type Announcement } from '@/types/announcements';

export interface AnnouncementPopupProps {
  announcement: Announcement;
  isOpen: boolean;
  onClose: () => void;
  onDismiss: (id: string) => void;
}

const AnnouncementPopup: React.FC<AnnouncementPopupProps> = ({ announcement, isOpen, onClose, onDismiss }) => {
  if (!announcement) return null;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'from-red-500 to-rose-600';
      case 'medium': return 'from-yellow-500 to-orange-600';
      case 'low': return 'from-blue-500 to-cyan-600';
      default: return 'from-gray-500 to-slate-600';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return AlertTriangle;
      case 'medium': return Info;
      case 'low': return CheckCircle;
      default: return Megaphone;
    }
  };

  const colorClass = getPriorityColor(announcement.priority || 'medium');
  const IconComponent = getPriorityIcon(announcement.priority || 'medium');

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      showCloseButton={false}
      className="bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center gap-3 p-6 border-b border-white/10">
          <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
            <IconComponent className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">{announcement.title}</h2>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="capitalize">
                {announcement.priority || 'medium'} Priority
              </Badge>
              {announcement.display_type && (
                <Badge variant="outline" className="text-white/70 border-white/20">
                  {announcement.display_type}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
              <div className="prose prose-invert max-w-none">
                <p className="text-white/90 leading-relaxed whitespace-pre-wrap">{announcement.content}</p>
              </div>

              {/* Tags */}
              {announcement.metadata?.tags && Array.isArray(announcement.metadata.tags) && announcement.metadata.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {announcement.metadata.tags.map((tag: string, index: number) => (
                    <Badge key={`tag-${tag}-${index}`} variant="outline" className="text-white/70 border-white/20">
                      <Tag className="w-3 h-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Meta Info */}
              <div className="flex items-center gap-4 text-sm text-white/60">
                <span>
                  Posted {new Date(announcement.created_at).toLocaleDateString()}
                </span>
                {announcement.target_audience && (
                  <span>
                    Target: {Array.isArray(announcement.target_audience)
                      ? announcement.target_audience.join(', ')
                      : announcement.target_audience}
                  </span>
                )}
              </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDismiss(announcement.id)}
                className="text-white border-white/20 hover:bg-white/10"
              >
                Dismiss
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Close
              </Button>
            </div>
            {announcement.is_featured && (
              <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                <Star className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default AnnouncementPopup;
