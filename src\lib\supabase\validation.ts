/**
 * Supabase Validation
 * 
 * This module provides validation schemas for Supabase data.
 */

import { z } from 'zod'
import type { Tables } from '@/types/database'

export type Festival = Tables<'festivals'>
export type Profile = Tables<'profiles'>

// Festival schema
export const festivalSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  description: z.string().nullable(),
  start_date: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: 'Invalid start date' }
  ),
  end_date: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: 'Invalid end date' }
  ),
  location: z.string().nullable(),
  website: z.string().url().nullable(),
  image_url: z.string().url().nullable(),
  created_at: z.string(),
  updated_at: z.string().nullable(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).nullable(),
  created_by: z.string().nullable(),
  featured: z.boolean(),
})

// Profile schema
export const profileSchema = z.object({
  id: z.string().uuid(),
  username: z.string(),
  email: z.string().email(),
  full_name: z.string().nullable(),
  avatar_url: z.string().url().nullable(),
  website: z.string().url().nullable(),
  bio: z.string().nullable(),
  role: z.enum(['SUPER_ADMIN', 'CONTENT_ADMIN', 'MODERATOR', 'USER']).nullable(),
  location: z.string().nullable(),
  interests: z.array(z.string()).nullable(),
  created_at: z.string(),
  updated_at: z.string(),
})

// Validate festival data
export function validateFestival(data: unknown): Festival {
  return festivalSchema.parse(data)
}

// Validate profile data
export function validateProfile(data: unknown): Profile {
  return profileSchema.parse(data)
}

// Safe parse with error handling
export function safeParseFestival(data: unknown): { success: boolean; data?: Festival; error?: z.ZodError } {
  const result = festivalSchema.safeParse(data)
  return result
}

// Safe parse with error handling
export function safeParseProfile(data: unknown): { success: boolean; data?: Profile; error?: z.ZodError } {
  const result = profileSchema.safeParse(data)
  return result
}
