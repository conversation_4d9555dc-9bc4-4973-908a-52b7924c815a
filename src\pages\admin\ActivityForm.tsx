/**
 * Activity Form Component
 *
 * Admin interface for creating and editing activities.
 * Uses activityService for data management and Zod for validation.
 */

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useNavigate, useParams } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { FormContainer } from '@/components/admin/FormContainer'
import { supabase } from '@/lib/supabase'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ImageUpload } from '@/components/ui/image-upload'

import type { Database } from '@/types/supabase'

// Supabase table type inference
type SupabaseActivity = Database['public']['Tables']['activities']['Insert']

// Form validation schema aligned with actual Supabase table
const activitySchema = z.object({
  id: z.string().optional(),
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  type: z.enum(['workshop', 'meetup', 'performance', 'game', 'social', 'food', 'other']),
  location: z.string().min(1, 'Location is required'),
  start_date: z.string().nullable().optional(),
  end_date: z.string().nullable().optional(),
  capacity: z.number().nullable().optional(),
  festival_id: z.string().nullable().optional(),
  parent_activity_id: z.string().nullable().optional(),
  image_url: z.string().nullable().optional(),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  is_featured: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  created_at: z.string().optional(),
  updated_at: z.string().nullable().optional(),
})

type ActivityFormData = z.infer<typeof activitySchema>

const STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', color: 'gray' },
  { value: 'published', label: 'Published', color: 'green' },
  { value: 'archived', label: 'Archived', color: 'red' }
]

export default function ActivityForm() {
  const { id } = useParams()
  const navigate = useNavigate()
  const { toast } = useToast()

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | undefined>()

  const form = useForm<ActivityFormData>({
    resolver: zodResolver(activitySchema),
    defaultValues: {
      title: '',
      description: '',
      type: 'workshop' as const,
      location: '',
      start_date: null,
      end_date: null,
      capacity: null,
      festival_id: null,
      parent_activity_id: null,
      image_url: null,
      status: 'draft',
      is_featured: false,
      tags: [],
    },
  })

  useEffect(() => {
    if (id) {
      loadActivity(id)
    }
  }, [id])

  const loadActivity = async (activityId: string) => {
    try {
      setLoading(true)
      setError(undefined)

      // Fetch the activity using actual database fields
      const { data: activity, error } = await supabase
        .from('activities')
        .select('*')
        .eq('id', activityId) // Fixed: Use UUID directly
        .single()

      if (error) throw error

      if (activity) {
        // Reset form with actual database fields and null safety
        form.reset({
          id: activity.id,
          title: activity.title, // Database uses 'title' field
          description: activity.description || '',
          type: (activity.type || 'other') as 'workshop' | 'meetup' | 'performance' | 'game' | 'social' | 'food' | 'other',
          location: activity.location || '',
          start_date: activity.start_date, // Database uses 'start_date' field
          end_date: activity.end_date, // Database uses 'end_date' field
          capacity: activity.capacity,
          festival_id: activity.festival_id || null, // Keep as UUID string or null
          parent_activity_id: activity.parent_activity_id || null, // Keep as UUID string or null
          image_url: activity.image_url || null,
          status: (activity.status as 'draft' | 'published' | 'archived') || 'draft',
          is_featured: activity.is_featured ?? false, // Use nullish coalescing for boolean
          created_at: activity.created_at || new Date().toISOString(),
          updated_at: activity.updated_at,
        })
      }
    } catch (error) {
      console.error('Error loading activity:', error)
      setError(error instanceof Error ? error : new Error('Failed to load activity'))
      toast({
        title: 'Error',
        description: 'Failed to load activity details',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: ActivityFormData) => {
    try {
      setLoading(true)
      setError(undefined)

      // Prepare activity data using actual database fields
      const activityData = {
        title: data.title, // Database uses 'title' field
        description: data.description,
        type: data.type as any, // Type will be validated by database enum
        location: data.location,
        start_date: data.start_date, // Database uses 'start_date' field
        end_date: data.end_date, // Database uses 'end_date' field
        capacity: data.capacity,
        festival_id: data.festival_id || null, // Database uses 'festival_id' field (UUID string)
        parent_activity_id: data.parent_activity_id || null, // Database uses UUID string
        image_url: data.image_url,
        status: data.status,
        is_featured: data.is_featured,
        updated_at: new Date().toISOString(),
      }

      // Insert or update the activity
      if (id) {
        const { error } = await supabase
          .from('activities')
          .update(activityData)
          .eq('id', id) // Fixed: Use UUID directly

        if (error) throw error
      } else {
        const { error } = await supabase
          .from('activities')
          .insert(activityData)

        if (error) throw error
      }

      toast({
        title: 'Success',
        description: id ? 'Activity updated' : 'Activity created',
        variant: 'default'
      })

      navigate('/admin/activities')
    } catch (error) {
      console.error('Error saving activity:', error)
      setError(error instanceof Error ? error : new Error('Failed to save activity'))
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save activity',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <FormContainer
      title={id ? 'Edit Activity' : 'Create Activity'}
      loading={loading}
      error={error}
      onReset={() => {
        setError(undefined)
        if (id) loadActivity(id)
      }}
      backTo="/admin/activities"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Activity Image */}
          <FormField
            control={form.control}
            name="image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Activity Image</FormLabel>
                <FormControl>
                  <ImageUpload
                    value={field.value}
                    onChange={field.onChange}
                    bucket="activity-images"
                    folder="activities"
                    placeholder="Upload an activity image"
                    className="w-full"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Activity Title */}
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Activity Title</FormLabel>
                <FormControl>
                  <Input placeholder="Enter activity title" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter activity description"
                    {...field}
                    rows={4}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location */}
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl>
                  <Input placeholder="Enter activity location" {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Type */}
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Activity Type</FormLabel>
                <FormControl>
                  <select
                    {...field}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="workshop">Workshop</option>
                    <option value="meetup">Meetup</option>
                    <option value="performance">Performance</option>
                    <option value="game">Game</option>
                    <option value="social">Social</option>
                    <option value="food">Food</option>
                    <option value="other">Other</option>
                  </select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Time Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="start_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="end_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Capacity */}
          <FormField
            control={form.control}
            name="capacity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Capacity</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min={1}
                    placeholder="Enter activity capacity"
                    {...field}
                    value={field.value || ''}
                    onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : null)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Festival ID */}
          <FormField
            control={form.control}
            name="festival_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Festival ID (optional)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter festival ID if this activity belongs to a festival"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Parent Activity ID */}
          <FormField
            control={form.control}
            name="parent_activity_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Parent Activity ID (optional)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter parent activity ID for sub-activities"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Publishing & Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Publishing & Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {STATUS_OPTIONS.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_featured"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Featured Activity</FormLabel>
                        <div className="text-sm text-muted-foreground">
                          Display this activity prominently
                        </div>
                      </div>
                      <FormControl>
                        <Input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="w-4 h-4"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-between gap-4 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/activities')}
            >
              Cancel
            </Button>

            <div className="flex space-x-2">
              {form.watch('status') !== 'draft' && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    form.setValue('status', 'draft');
                    form.handleSubmit(onSubmit)();
                  }}
                  disabled={loading}
                >
                  Save as Draft
                </Button>
              )}
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' :
                 form.watch('status') === 'published' ? 'Update Activity' :
                 'Save Activity'}
              </Button>
              {form.watch('status') === 'draft' && (
                <Button
                  type="button"
                  onClick={() => {
                    form.setValue('status', 'published');
                    form.handleSubmit(onSubmit)();
                  }}
                  disabled={loading}
                >
                  Publish Now
                </Button>
              )}
            </div>
          </div>
        </form>
      </Form>
    </FormContainer>
  )
}
