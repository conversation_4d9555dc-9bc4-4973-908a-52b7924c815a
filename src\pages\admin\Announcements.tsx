import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase/core-client';
import { useProfile } from '../../hooks/useProfile';
import { isAdminRole } from '@/lib/utils/auth';
import type { DatabaseAnnouncement } from '@/types/database';
import { toast } from 'react-hot-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const AdminAnnouncements: React.FC = () => {
  const navigate = useNavigate();
  const { profile, isLoading: profileLoading } = useProfile();
  const loading = profileLoading; // Ensure loading is correctly defined
  const [announcements, setAnnouncements] = React.useState<DatabaseAnnouncement[]>([]);
  const [newAnnouncement, setNewAnnouncement] = useState({
    title: '',
    content: '',
    priority: 'medium' as const,
    target_audience: 'all' as const,
    status: 'draft' as 'draft' | 'published' | 'archived',
    is_featured: false,
    tags: [] as string[],
    start_date: '',
    end_date: ''
  });
  const [editingAnnouncement, setEditingAnnouncement] = useState<DatabaseAnnouncement | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect non-admin users
  React.useEffect(() => {
    if (!loading && !isAdminRole(profile?.role)) {
      navigate('/');
    }
  }, [loading, profile, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      console.log('Creating announcement with data:', {
        title: newAnnouncement.title,
        content: newAnnouncement.content,
        priority: newAnnouncement.priority,
        target_audience: [newAnnouncement.target_audience], // Convert string to array
        status: newAnnouncement.status,
        is_featured: newAnnouncement.is_featured,
        tags: newAnnouncement.tags,
        active: newAnnouncement.status === 'published',
        start_date: newAnnouncement.start_date || null,
        end_date: newAnnouncement.end_date || null,
        created_by: profile?.id
      });

      const announcementData = {
        title: newAnnouncement.title,
        content: newAnnouncement.content,
        priority: newAnnouncement.priority,
        target_audience: [newAnnouncement.target_audience], // Fixed: Convert string to array
        status: newAnnouncement.status,
        is_featured: newAnnouncement.is_featured,
        tags: newAnnouncement.tags,
        active: newAnnouncement.status === 'published',
        start_date: newAnnouncement.start_date || null,
        end_date: newAnnouncement.end_date || null,
        created_by: profile?.id
        // Removed: 'type' and 'is_pinned' as they don't exist in database
      };

      const { error } = await supabase
        .from('announcements')
        .insert([announcementData]);

      if (error) throw error;

      toast.success(`Announcement ${newAnnouncement.status === 'published' ? 'published' : 'saved as draft'} successfully!`);

      // Reset form
      setNewAnnouncement({
        title: '',
        content: '',
        priority: 'medium',
        target_audience: 'all',
        status: 'draft',
        is_featured: false,
        tags: [],
        start_date: '',
        end_date: ''
      });

      // Refresh announcements
      fetchAnnouncements();
    } catch (error) {
      console.error('Error creating announcement:', error);
      toast.error('Failed to create announcement');
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchAnnouncements = async () => {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAnnouncements(data || []);
    } catch (error) {
      console.error('Error fetching announcements:', error);
    }
  };

  const toggleAnnouncementStatus = async (id: string, currentStatus: boolean) => {
    try {
      const newStatus = currentStatus ? 'archived' : 'published';
      const { error } = await supabase
        .from('announcements')
        .update({
          status: newStatus,
          active: newStatus === 'published',
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;
      toast.success(`Announcement ${newStatus}!`);
      fetchAnnouncements();
    } catch (error) {
      console.error('Error updating announcement:', error);
      toast.error('Failed to update announcement');
    }
  };

  const pushAnnouncementNow = async (id: string) => {
    try {
      // Update announcement to be active and set display_type to banner for immediate visibility
      const { error } = await supabase
        .from('announcements')
        .update({
          status: 'published',
          active: true,
          display_type: 'banner',
          priority: 'high', // Make it high priority for immediate attention
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;

      // Clear any dismissed announcements from localStorage to force re-display
      localStorage.removeItem('dismissed_announcements');

      toast.success('Announcement pushed to all users immediately!');
      fetchAnnouncements();
    } catch (error) {
      console.error('Error pushing announcement:', error);
      toast.error('Failed to push announcement');
    }
  };

  const deleteAnnouncement = async (id: string) => {
    if (!confirm('Are you sure you want to delete this announcement?')) return;

    try {
      const { error } = await supabase
        .from('announcements')
        .delete()
        .eq('id', id);

      if (error) throw error;
      toast.success('Announcement deleted successfully!');
      fetchAnnouncements();
    } catch (error) {
      console.error('Error deleting announcement:', error);
      toast.error('Failed to delete announcement');
    }
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setNewAnnouncement(prev => ({ ...prev, tags }));
  };

  const handlePublish = () => {
    setNewAnnouncement(prev => ({ ...prev, status: 'published' }));
    // Form will be submitted with published status
  };

  const handleSaveDraft = () => {
    setNewAnnouncement(prev => ({ ...prev, status: 'draft' }));
    // Form will be submitted with draft status
  };

  React.useEffect(() => {
    fetchAnnouncements();
  }, []);

  if (loading || !profile || !isAdminRole(profile.role)) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Manage Announcements</h1>
        {newAnnouncement.status && (
          <Badge variant={newAnnouncement.status === 'published' ? 'default' : 'secondary'}>
            {newAnnouncement.status.charAt(0).toUpperCase() + newAnnouncement.status.slice(1)}
          </Badge>
        )}
      </div>

      {/* Create Announcement Form */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Create New Announcement</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>

              <div>
                <label className="block text-sm font-medium mb-2" htmlFor="announcement-title">Title *</label>
                <Input
                  id="announcement-title"
                  type="text"
                  placeholder="Enter announcement title"
                  value={newAnnouncement.title}
                  onChange={(e) => setNewAnnouncement(prev => ({ ...prev, title: e.target.value }))}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" htmlFor="announcement-content">Content *</label>
                <Textarea
                  id="announcement-content"
                  placeholder="Enter announcement content"
                  value={newAnnouncement.content}
                  onChange={(e) => setNewAnnouncement(prev => ({ ...prev, content: e.target.value }))}
                  rows={4}
                  required
                />
              </div>
            </div>

            {/* Settings & Targeting */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Settings & Targeting</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2" htmlFor="announcement-priority">Priority</label>
                  <Select
                    value={newAnnouncement.priority}
                    onValueChange={(value) => setNewAnnouncement(prev => ({ ...prev, priority: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2" htmlFor="announcement-audience">Target Audience</label>
                  <Select
                    value={newAnnouncement.target_audience}
                    onValueChange={(value) => setNewAnnouncement(prev => ({ ...prev, target_audience: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select audience" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="admins">Admins Only</SelectItem>
                      <SelectItem value="users">Regular Users</SelectItem>
                      <SelectItem value="festival_family">Festival Family</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2" htmlFor="announcement-status">Status</label>
                  <Select
                    value={newAnnouncement.status}
                    onValueChange={(value) => setNewAnnouncement(prev => ({ ...prev, status: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" htmlFor="announcement-tags">Tags</label>
                <Input
                  id="announcement-tags"
                  placeholder="Enter tags separated by commas..."
                  value={newAnnouncement.tags.join(', ')}
                  onChange={(e) => handleTagsChange(e.target.value)}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Separate tags with commas (e.g., "urgent, maintenance, update")
                </p>
              </div>

              <div className="flex items-center space-x-6">
                <label className="flex items-center space-x-2">
                  <Input
                    type="checkbox"
                    checked={newAnnouncement.is_featured}
                    onChange={(e) => setNewAnnouncement(prev => ({ ...prev, is_featured: e.target.checked }))}
                    className="w-4 h-4"
                  />
                  <span className="text-sm">Featured announcement</span>
                </label>
              </div>
            </div>

            {/* Scheduling */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Scheduling (Optional)</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2" htmlFor="announcement-start">Start Date</label>
                  <Input
                    id="announcement-start"
                    type="datetime-local"
                    value={newAnnouncement.start_date}
                    onChange={(e) => setNewAnnouncement(prev => ({ ...prev, start_date: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2" htmlFor="announcement-end">End Date</label>
                  <Input
                    id="announcement-end"
                    type="datetime-local"
                    value={newAnnouncement.end_date}
                    onChange={(e) => setNewAnnouncement(prev => ({ ...prev, end_date: e.target.value }))}
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setNewAnnouncement({
                    title: '',
                    content: '',
                    priority: 'medium',
                    target_audience: 'all',
                    status: 'draft',
                    is_featured: false,
                    tags: [],
                    start_date: '',
                    end_date: ''
                  });
                }}
                disabled={isSubmitting}
              >
                Reset Form
              </Button>

              <div className="flex space-x-2">
                {newAnnouncement.status !== 'draft' && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setNewAnnouncement(prev => ({ ...prev, status: 'draft' }));
                      handleSubmit(new Event('submit') as any);
                    }}
                    disabled={isSubmitting}
                  >
                    Save as Draft
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  variant={newAnnouncement.status === 'published' ? 'default' : 'secondary'}
                >
                  {isSubmitting ? 'Saving...' :
                   newAnnouncement.status === 'published' ? 'Publish Announcement' :
                   'Save Announcement'}
                </Button>
                {newAnnouncement.status === 'draft' && (
                  <Button
                    type="button"
                    onClick={() => {
                      setNewAnnouncement(prev => ({ ...prev, status: 'published' }));
                      handleSubmit(new Event('submit') as any);
                    }}
                    disabled={isSubmitting}
                  >
                    Publish Now
                  </Button>
                )}
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Announcements List */}
      <Card>
        <CardHeader>
          <CardTitle>Existing Announcements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left">Title</th>
                  <th className="px-4 py-3 text-left">Type</th>
                  <th className="px-4 py-3 text-left">Priority</th>
                  <th className="px-4 py-3 text-left">Status</th>
                  <th className="px-4 py-3 text-left">Created</th>
                  <th className="px-4 py-3 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {announcements.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                      No announcements found. Create your first announcement above.
                    </td>
                  </tr>
                ) : (
                  announcements.map((announcement) => (
                    <tr key={announcement.id} className="border-b">
                      <td className="px-4 py-4">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{announcement.title}</span>
                          {announcement.is_pinned && (
                            <Badge variant="outline" className="text-xs">Pinned</Badge>
                          )}
                          {announcement.is_featured && (
                            <Badge variant="outline" className="text-xs">Featured</Badge>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <Badge variant="outline" className="capitalize">
                          {announcement.type || 'info'}
                        </Badge>
                      </td>
                      <td className="px-4 py-4">
                        <Badge
                          variant={
                            announcement.priority === 'high' ? 'destructive' :
                            announcement.priority === 'medium' ? 'default' : 'secondary'
                          }
                          className="capitalize"
                        >
                          {announcement.priority || 'low'}
                        </Badge>
                      </td>
                      <td className="px-4 py-4">
                        <Badge
                          variant={
                            announcement.status === 'published' ? 'default' :
                            announcement.status === 'draft' ? 'secondary' : 'destructive'
                          }
                          className="capitalize"
                        >
                          {announcement.status || (announcement.active ? 'published' : 'draft')}
                        </Badge>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-500">
                        {announcement.created_at ? new Date(announcement.created_at).toLocaleDateString() : 'No date'}
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleAnnouncementStatus(
                              announcement.id,
                              announcement.status === 'published' || (announcement.active ?? false)
                            )}
                          >
                            {announcement.status === 'published' || announcement.active ? 'Archive' : 'Publish'}
                          </Button>
                          {(announcement.status === 'published' || announcement.active) && (
                            <Button
                              size="sm"
                              variant="default"
                              onClick={() => pushAnnouncementNow(announcement.id)}
                              className="bg-orange-600 hover:bg-orange-700"
                            >
                              Push Now
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => deleteAnnouncement(announcement.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminAnnouncements;
