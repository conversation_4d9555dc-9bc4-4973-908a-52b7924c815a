/**
 * Activity Attendance Hook
 * 
 * React hook for managing activity attendance and finding activity buddies.
 * Follows Festival Family's established hook patterns and error handling.
 * 
 * @module useActivityAttendance
 * @version 1.0.0
 * <AUTHOR> Family Team
 */

import { useState, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { activityAttendanceService } from '@/lib/supabase/services'
import { useAuth } from '@/providers/ConsolidatedAuthProvider'
import type { 
  ActivityAttendance, 
  AttendanceStatus, 
  ActivityWithAttendance,
  ActivityCoordinationSuggestion
} from '@/lib/supabase/activity-coordination/types'
import type { Profile } from '@/types'

/**
 * Hook for managing user's attendance for activities
 */
export function useActivityAttendance(activityId: string) {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  // Get user's attendance status for this activity
  const { data: userAttendance, isLoading: isLoadingAttendance } = useQuery({
    queryKey: ['activity-attendance', activityId, user?.id],
    queryFn: () => user ? activityAttendanceService.getUserAttendance(user.id, activityId) : null,
    enabled: !!user && !!activityId,
    select: (response) => response?.data
  })

  // Get all attendees for this activity
  const { data: attendees, isLoading: isLoadingAttendees } = useQuery({
    queryKey: ['activity-attendees', activityId],
    queryFn: () => activityAttendanceService.getActivityAttendees(activityId),
    enabled: !!activityId,
    select: (response) => response?.data || []
  })

  // Get activity with attendance data
  const { data: activityWithAttendance, isLoading: isLoadingActivity } = useQuery({
    queryKey: ['activity-with-attendance', activityId, user?.id],
    queryFn: () => activityAttendanceService.getActivityWithAttendance(activityId, user?.id),
    enabled: !!activityId,
    select: (response) => response?.data
  })

  // Set attendance mutation
  const setAttendanceMutation = useMutation({
    mutationFn: ({ status, notes }: { status: AttendanceStatus; notes?: string }) => {
      if (!user) throw new Error('User not authenticated')
      return activityAttendanceService.setAttendance(user.id, activityId, status, notes)
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['activity-attendance', activityId] })
      queryClient.invalidateQueries({ queryKey: ['activity-attendees', activityId] })
      queryClient.invalidateQueries({ queryKey: ['activity-with-attendance', activityId] })
      queryClient.invalidateQueries({ queryKey: ['user-activity-schedule', user?.id] })
    }
  })

  // Remove attendance mutation
  const removeAttendanceMutation = useMutation({
    mutationFn: () => {
      if (!user) throw new Error('User not authenticated')
      return activityAttendanceService.removeAttendance(user.id, activityId)
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['activity-attendance', activityId] })
      queryClient.invalidateQueries({ queryKey: ['activity-attendees', activityId] })
      queryClient.invalidateQueries({ queryKey: ['activity-with-attendance', activityId] })
      queryClient.invalidateQueries({ queryKey: ['user-activity-schedule', user?.id] })
    }
  })

  // Helper functions
  const setAttendance = useCallback((status: AttendanceStatus, notes?: string) => {
    setAttendanceMutation.mutate({ status, notes })
  }, [setAttendanceMutation])

  const removeAttendance = useCallback(() => {
    removeAttendanceMutation.mutate()
  }, [removeAttendanceMutation])

  const isGoing = userAttendance?.status === 'going'
  const isInterested = userAttendance?.status === 'interested'
  const isMaybe = userAttendance?.status === 'maybe'
  const hasAttendance = !!userAttendance

  return {
    // Data
    userAttendance,
    attendees,
    activityWithAttendance,
    
    // Loading states
    isLoading: isLoadingAttendance || isLoadingAttendees || isLoadingActivity,
    isLoadingAttendance,
    isLoadingAttendees,
    isLoadingActivity,
    
    // Mutation states
    isSettingAttendance: setAttendanceMutation.isPending,
    isRemovingAttendance: removeAttendanceMutation.isPending,
    
    // Helper states
    isGoing,
    isInterested,
    isMaybe,
    hasAttendance,
    
    // Actions
    setAttendance,
    removeAttendance,
    
    // Errors
    error: setAttendanceMutation.error || removeAttendanceMutation.error
  }
}

/**
 * Hook for finding activity buddies
 */
export function useActivityBuddies(activityId: string) {
  const { user } = useAuth()

  const { data: buddies, isLoading, error } = useQuery({
    queryKey: ['activity-buddies', activityId, user?.id],
    queryFn: () => user ? activityAttendanceService.findActivityBuddies(user.id, activityId) : null,
    enabled: !!user && !!activityId,
    select: (response) => response?.data || []
  })

  return {
    buddies,
    isLoading,
    error
  }
}

/**
 * Hook for user's activity schedule
 */
export function useUserActivitySchedule(festivalId?: string, statusFilter?: AttendanceStatus[]) {
  const { user } = useAuth()

  const { data: schedule, isLoading, error } = useQuery({
    queryKey: ['user-activity-schedule', user?.id, festivalId, statusFilter],
    queryFn: () => user ? activityAttendanceService.getUserActivitySchedule(user.id, festivalId, statusFilter) : null,
    enabled: !!user,
    select: (response) => response?.data || []
  })

  return {
    schedule,
    isLoading,
    error
  }
}

/**
 * Hook for activity coordination suggestions
 */
export function useActivitySuggestions(festivalId: string, limit = 10) {
  const { user } = useAuth()

  const { data: suggestions, isLoading, error } = useQuery({
    queryKey: ['activity-suggestions', user?.id, festivalId, limit],
    queryFn: () => user ? activityAttendanceService.getActivitySuggestions(user.id, festivalId, limit) : null,
    enabled: !!user && !!festivalId,
    select: (response) => response?.data || []
  })

  return {
    suggestions,
    isLoading,
    error
  }
}

/**
 * Hook for live activity status
 */
export function useLiveActivityStatus(activityId: string) {
  const [isLive, setIsLive] = useState(false)

  const { data: liveStatus, isLoading, error } = useQuery({
    queryKey: ['live-activity-status', activityId],
    queryFn: () => activityAttendanceService.getLiveActivityStatus(activityId),
    enabled: !!activityId && isLive,
    refetchInterval: isLive ? 30000 : false, // Refetch every 30 seconds when live
    select: (response) => response?.data
  })

  const startLiveUpdates = useCallback(() => {
    setIsLive(true)
  }, [])

  const stopLiveUpdates = useCallback(() => {
    setIsLive(false)
  }, [])

  return {
    liveStatus,
    isLoading,
    error,
    isLive,
    startLiveUpdates,
    stopLiveUpdates
  }
}

/**
 * Hook for batch attendance operations
 */
export function useBatchAttendance() {
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const batchSetAttendanceMutation = useMutation({
    mutationFn: async (operations: { activityId: string; status: AttendanceStatus; notes?: string }[]) => {
      if (!user) throw new Error('User not authenticated')
      
      const results = await Promise.all(
        operations.map(op => 
          activityAttendanceService.setAttendance(user.id, op.activityId, op.status, op.notes)
        )
      )
      
      return results
    },
    onSuccess: () => {
      // Invalidate all attendance-related queries
      queryClient.invalidateQueries({ queryKey: ['activity-attendance'] })
      queryClient.invalidateQueries({ queryKey: ['activity-attendees'] })
      queryClient.invalidateQueries({ queryKey: ['activity-with-attendance'] })
      queryClient.invalidateQueries({ queryKey: ['user-activity-schedule'] })
    }
  })

  const setBatchAttendance = useCallback((operations: { activityId: string; status: AttendanceStatus; notes?: string }[]) => {
    batchSetAttendanceMutation.mutate(operations)
  }, [batchSetAttendanceMutation])

  return {
    setBatchAttendance,
    isLoading: batchSetAttendanceMutation.isPending,
    error: batchSetAttendanceMutation.error
  }
}
