import React from 'react';
import { Lightbulb, Tag, Clock, Heart, Share2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ResponsiveModal } from '@/components/design-system/ResponsiveModal';

interface Tip {
  id: string;
  title: string;
  content: string;
  description?: string;
  category?: string;
  tags?: string[];
  is_featured?: boolean;
  view_count?: number;
  helpful_count?: number;
  created_at: string;
}

interface TipDetailsModalProps {
  tip: Tip | null;
  isOpen: boolean;
  onClose: () => void;
}

export const TipDetailsModal: React.FC<TipDetailsModalProps> = ({
  tip,
  isOpen,
  onClose,
}) => {
  if (!tip) return null;

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'SURVIVAL': return 'from-green-500 to-emerald-600';
      case 'SOCIAL': return 'from-blue-500 to-cyan-600';
      case 'BUDGET': return 'from-yellow-500 to-orange-600';
      case 'COMFORT': return 'from-purple-500 to-pink-600';
      case 'EXPERIENCE': return 'from-indigo-500 to-purple-600';
      case 'SAFETY': return 'from-red-500 to-rose-600';
      default: return 'from-gray-500 to-slate-600';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'SURVIVAL': return 'Festival Survival';
      case 'SOCIAL': return 'Social & Networking';
      case 'BUDGET': return 'Budget & Money';
      case 'COMFORT': return 'Comfort & Gear';
      case 'EXPERIENCE': return 'Experience & Fun';
      case 'SAFETY': return 'Safety & Health';
      default: return category;
    }
  };

  const colorClass = getCategoryColor(tip.category || '');

  return (
    <ResponsiveModal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      showCloseButton={false}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 bg-gradient-to-br ${colorClass} rounded-lg flex items-center justify-center`}>
              <Lightbulb className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{tip.title}</h2>
              {tip.category && (
                <Badge variant="secondary" className="mt-1">
                  {getCategoryLabel(tip.category)}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {tip.description && (
            <div className="bg-white/5 rounded-lg p-4">
              <p className="text-white/80 text-sm">{tip.description}</p>
            </div>
          )}

          <div className="prose prose-invert max-w-none">
            <p className="text-white/90 leading-relaxed">{tip.content}</p>
          </div>

          {/* Tags */}
          {tip.tags && tip.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tip.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-white/70 border-white/20">
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center gap-4 text-sm text-white/60">
            {tip.view_count !== undefined && (
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {tip.view_count} views
              </span>
            )}
            {tip.helpful_count !== undefined && (
              <span className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {tip.helpful_count} helpful
              </span>
            )}
            <span>
              {new Date(tip.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="text-white border-white/20 hover:bg-white/10"
              >
                <Heart className="w-4 h-4 mr-2" />
                Helpful
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="text-white border-white/20 hover:bg-white/10"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button
                variant="outline"
                onClick={onClose}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Close
              </Button>
            </div>
            {tip.is_featured && (
              <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                Featured
              </Badge>
            )}
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

export default TipDetailsModal;
