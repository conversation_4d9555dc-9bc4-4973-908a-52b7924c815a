-- Create communities table for managing community groups and external links
CREATE TABLE IF NOT EXISTS public.communities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'general',
    type TEXT DEFAULT 'external' CHECK (type IN ('external', 'internal')),
    external_url TEXT,
    member_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for communities
ALTER TABLE public.communities ENABLE ROW LEVEL SECURITY;

-- Policy: Everyone can view active communities
CREATE POLICY "Everyone can view active communities"
ON public.communities
FOR SELECT
USING (is_active = true);

-- Policy: <PERSON><PERSON> can manage all communities
CREATE POLICY "Ad<PERSON> can manage communities"
ON public.communities
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('SUPER_ADMIN', 'CONTENT_ADMIN')
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_communities_active ON public.communities(is_active);
CREATE INDEX IF NOT EXISTS idx_communities_featured ON public.communities(is_featured);
CREATE INDEX IF NOT EXISTS idx_communities_category ON public.communities(category);
CREATE INDEX IF NOT EXISTS idx_communities_type ON public.communities(type);

-- Insert default communities
INSERT INTO public.communities (name, description, category, type, external_url, member_count, is_featured, is_active) VALUES
('Discord Community', 'Join our main Discord server for real-time chat and voice channels', 'Discord', 'external', 'https://discord.gg/festivalfamily', 150, true, true),
('Telegram Updates', 'Get instant notifications and join quick discussions', 'Telegram', 'external', 'https://t.me/festivalfamily', 89, false, true),
('WhatsApp Groups', 'Connect on WhatsApp for local festival coordination', 'WhatsApp', 'external', 'https://wa.me/group/festivalfamily', 234, true, true),
('Facebook Community', 'Share photos, experiences and connect with fellow festival lovers', 'Facebook', 'external', 'https://facebook.com/groups/festivalfamily', 456, false, true),
('Reddit Community', 'Discuss festivals, share tips and connect on Reddit', 'Reddit', 'external', 'https://reddit.com/r/festivalfamily', 123, false, true);
