import React, { createContext, useContext, useEffect, useState, startTransition } from 'react';
import { Session, User, UserResponse } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { toast } from 'react-hot-toast';
import { setSentryUser, addBreadcrumb } from '@/lib/sentry';
import { isAdminRole } from '@/lib/utils/auth';

// Define profile type
export type UserProfile = {
  id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  email?: string;
  role?: string;
  created_at?: string;
  updated_at?: string;
  community_rules_accepted?: boolean;
  community_rules_accepted_at?: string;
};

// Define the context type with enhanced functionality
type AuthContextType = {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  isAdmin: boolean;
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<UserResponse>;
  signUp: (email: string, password: string, userData?: Partial<UserProfile>) => Promise<UserResponse>;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
};

// Create the context with default values
const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  profile: null,
  loading: true,
  isAdmin: false,
  signIn: async () => ({ data: { user: null as any }, error: null }),
  signUp: async () => ({ data: { user: null as any }, error: null }),
  signOut: async () => {},
  refreshProfile: async () => {},
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Consolidated Auth Provider component
export const ConsolidatedAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  // Function to fetch user profile from the database with timeout
  const fetchProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      console.log(`Fetching profile for user: ${userId}`);

      // CRITICAL FIX: Ensure we have a valid session before making the query
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      if (!currentSession) {
        console.error('No active session found for profile fetch');
        return null;
      }

      console.log('✅ Active session confirmed for profile fetch');

      // Fixed timeout implementation with Promise.race
      const timeoutMs = 2000; // Reduced to 2 seconds for faster response

      const timeoutPromise = new Promise<{ data: null, error: { message: string, code: string } }>((_, reject) => {
        setTimeout(() => {
          console.log(`Profile fetch timeout triggered after ${timeoutMs}ms`);
          reject(new Error('TIMEOUT'));
        }, timeoutMs);
      });

      // Execute the query with proper error handling
      let data, error;
      try {
        const queryPromise = supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        const result = await Promise.race([queryPromise, timeoutPromise]);
        data = result.data;
        error = result.error;
      } catch (timeoutError: any) {
        if (timeoutError?.message === 'TIMEOUT') {
          console.warn('Profile fetch timed out - using fallback approach');
          data = null;
          error = { message: 'Profile fetch timeout', code: 'TIMEOUT' };
        } else {
          throw timeoutError;
        }
      }

      if (error) {
        console.error('Error fetching profile:', error);

        // Check if it's a timeout error
        if (error.code === 'TIMEOUT') {
          console.warn('Profile fetch timed out - using fallback approach');
          return null;
        }

        // Check if it's a "not found" error (which is expected for new users)
        if (error.code === 'PGRST116' || error.message?.includes('No rows found')) {
          console.log('Profile not found - this is expected for new users');
          return null;
        }

        // Check for network/connectivity errors
        if (error.message?.includes('fetch') || error.message?.includes('network') || error.message?.includes('Failed to fetch')) {
          console.error('Network error fetching profile - user may be offline or have connectivity issues');
          return null;
        }

        // For other errors, log and return null
        console.error('Database error fetching profile:', error);
        return null;
      }

      console.log('Profile fetched successfully:', data);
      return data as UserProfile;
    } catch (error) {
      console.error('Exception while fetching profile:', error);
      return null;
    }
  };

  // Function to handle auth state change
  const handleAuthChange = async (session: Session | null) => {
    console.log('Auth state changed:', session ? 'signed in' : 'signed out');

    // Use startTransition to prevent React 18 suspension errors during navigation
    startTransition(() => {
      setSession(session);
      setUser(session?.user ?? null);
    });

    if (session?.user) {
      console.log('Fetching profile for authenticated user:', session.user.email);

      // IMMEDIATE ADMIN CHECK: Handle known admin user before profile fetch
      const isKnownAdmin = session.user.email === '<EMAIL>' ||
                          session.user.id === '7f4f5eea-3974-4e2f-a324-00fe5458750d';

      if (isKnownAdmin) {
        console.log('🔑 IMMEDIATE ADMIN OVERRIDE: Setting known admin user with SUPER_ADMIN role');
        const adminProfile: UserProfile = {
          id: session.user.id,
          email: session.user.email || '',
          username: 'admin',
          full_name: 'System Administrator',
          role: 'SUPER_ADMIN',
          created_at: new Date().toISOString(),
        };

        // Set admin profile immediately to unblock UI
        startTransition(() => {
          setProfile(adminProfile);
          setIsAdmin(true);
          setLoading(false); // CRITICAL: Set loading to false for admin override
        });

        console.log('✅ Admin profile set immediately - bypassing database fetch');

        // Skip database sync for admin - just use in-memory profile
        console.log('🔄 Admin profile using in-memory data only');

        // Set Sentry user context
        setSentryUser({
          id: session.user.id,
          email: session.user.email,
          username: adminProfile.username,
          role: adminProfile.role,
        });

        addBreadcrumb('Admin user authenticated', 'auth', 'info');

        // Skip normal profile fetch for admin
        return;
      }

      const profileData = await fetchProfile(session.user.id);

      if (profileData) {
        // Use startTransition for profile-related state updates
        startTransition(() => {
          setProfile(profileData);
          setIsAdmin(isAdminRole(profileData.role));
        });

        console.log(`User role: ${profileData.role}, isAdmin: ${isAdminRole(profileData.role)}`);

        // Set Sentry user context for error tracking
        setSentryUser({
          id: session.user.id,
          email: session.user.email,
          username: profileData.username,
          role: profileData.role,
        });

        addBreadcrumb('User authenticated', 'auth', 'info');
      } else {
        // Profile not found - create a basic profile for the user
        console.warn('No profile found for user, creating basic profile');

        try {
          // SPECIAL CASE: Handle known admin user (more flexible check)
          const isKnownAdmin = session.user.email === '<EMAIL>' ||
                              session.user.id === '7f4f5eea-3974-4e2f-a324-00fe5458750d';

          const basicProfile: UserProfile = {
            id: session.user.id,
            email: session.user.email || '',
            username: session.user.email?.split('@')[0] || 'user',
            role: isKnownAdmin ? 'SUPER_ADMIN' : 'user', // Use SUPER_ADMIN for known admin
            created_at: new Date().toISOString(),
          };

          if (isKnownAdmin) {
            console.log('🔑 ADMIN OVERRIDE: Setting known admin user with SUPER_ADMIN role');
          }

          // Try to create the profile in the database (non-blocking)
          try {
            const { error: createError } = await supabase
              .from('profiles')
              .insert({
                id: basicProfile.id,
                email: basicProfile.email || '',
                username: basicProfile.username || '',
                full_name: basicProfile.username || '',
                role: basicProfile.role || 'USER',
                created_at: basicProfile.created_at,
                interests: []
              });

            if (createError) {
              console.error('Error creating basic profile in database:', createError);
              console.log('Continuing with in-memory profile...');
            } else {
              console.log('Basic profile created successfully in database');
            }
          } catch (dbError) {
            console.error('Database operation failed, continuing with in-memory profile:', dbError);
          }

          // ALWAYS set the basic profile in state to unblock UI
          startTransition(() => {
            setProfile(basicProfile);
            setIsAdmin(isAdminRole(basicProfile.role));
          });

          console.log(`Basic profile set - role: ${basicProfile.role}, isAdmin: ${isAdminRole(basicProfile.role)}`);
        } catch (error) {
          console.error('Exception creating basic profile:', error);

          // Even if everything fails, set minimal profile to unblock UI
          const minimalProfile: UserProfile = {
            id: session.user.id,
            email: session.user.email || '',
            username: session.user.email?.split('@')[0] || 'user',
            role: 'user',
          };

          startTransition(() => {
            setProfile(minimalProfile);
            setIsAdmin(false);
          });

          console.log('Minimal profile set as fallback');
        }
      }
    } else {
      // Use startTransition for sign out state updates
      startTransition(() => {
        setProfile(null);
        setIsAdmin(false);
      });

      // Clear Sentry user context
      setSentryUser({ id: 'anonymous' });
      addBreadcrumb('User signed out', 'auth', 'info');
    }

    // CRITICAL: Always set loading to false to unblock UI
    // Use setTimeout to ensure this runs after all other state updates
    setTimeout(() => {
      startTransition(() => {
        setLoading(false);
      });
      console.log('Auth state change completed, loading set to false');
    }, 100);

    // EMERGENCY FALLBACK: Force loading to false after maximum timeout
    setTimeout(() => {
      startTransition(() => {
        setLoading(false);
      });
      console.log('EMERGENCY: Forcing loading to false after 8 seconds');
    }, 8000);
  };

  // Initialize auth state and set up listener
  useEffect(() => {
    console.log('ConsolidatedAuthProvider: Initializing authentication...');
    
    // Get current host information
    const currentOrigin = window.location.origin;
    const currentHost = window.location.host;
    console.log(`Auth provider initialized with host: ${currentHost}, origin: ${currentOrigin}`);

    // Function to get initial session
    const getInitialSession = async () => {
      try {
        console.log('Getting initial session...');
        
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting initial session:', error);
          setLoading(false);
          return;
        }

        console.log('Initial session retrieved:', session ? 'found' : 'none');
        await handleAuthChange(session);
      } catch (error) {
        console.error('Exception getting initial session:', error);
        setLoading(false);
      }
    };

    // Get initial session
    getInitialSession();

    // Set up the auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log(`Auth event: ${event}`, session ? 'with session' : 'without session');

        try {
          await handleAuthChange(session);
        } catch (error) {
          console.error(`Error handling auth event ${event}:`, error);
        }
      }
    );

    // Clean up the listener when the component unmounts
    return () => {
      console.log('Cleaning up auth listener');
      authListener?.subscription.unsubscribe();
    };
  }, []);

  // Sign in function
  const signIn = async (email: string, password: string, rememberMe: boolean = false): Promise<UserResponse> => {
    try {
      console.log('Attempting sign in for:', email);
      setLoading(true);

      const response = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (response.error) {
        console.error('Sign in error:', response.error);
        toast.error(response.error.message || 'Sign in failed');
        return response;
      }

      if (response.data.user) {
        console.log('Sign in successful for user:', response.data.user.id);
        toast.success('Welcome back!');
      }

      return response;
    } catch (error) {
      console.error('Exception during sign in:', error);
      const authError = {
        message: 'An unexpected error occurred during sign in',
        code: 'UNEXPECTED_ERROR',
        status: 500,
        __isAuthError: true
      } as any;
      toast.error(authError.message);
      return { data: { user: null }, error: authError };
    } finally {
      setLoading(false);
    }
  };

  // Sign up function
  const signUp = async (email: string, password: string, userData?: Partial<UserProfile>): Promise<UserResponse> => {
    try {
      console.log('Attempting sign up for:', email);
      setLoading(true);

      const response = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData || {}
        }
      });

      if (response.error) {
        console.error('Sign up error:', response.error);
        toast.error(response.error.message || 'Sign up failed');
        return response;
      }

      if (response.data.user) {
        console.log('Sign up successful for user:', response.data.user.id);
        
        // Create profile if user data provided
        if (userData && (userData.username || userData.full_name)) {
          try {
            const { error: profileError } = await supabase
              .from('profiles')
              .insert({
                id: response.data.user.id,
                email: response.data.user.email || '',
                username: userData.username || '',
                full_name: userData.full_name || null,
                avatar_url: userData.avatar_url || null,
                role: 'USER', // Default role
                interests: []
              });

            if (profileError) {
              console.error('Error creating profile:', profileError);
            } else {
              console.log('Profile created successfully');
            }
          } catch (profileError) {
            console.error('Exception creating profile:', profileError);
          }
        }

        toast.success('Account created successfully! Please check your email for verification.');
      }

      return response as UserResponse;
    } catch (error) {
      console.error('Exception during sign up:', error);
      const authError = {
        message: 'An unexpected error occurred during sign up',
        code: 'UNEXPECTED_ERROR',
        status: 500,
        __isAuthError: true
      } as any;
      toast.error(authError.message);
      return { data: { user: null }, error: authError };
    } finally {
      setLoading(false);
    }
  };

  // Sign out function
  const signOut = async (): Promise<void> => {
    try {
      console.log('Signing out user...');
      setLoading(true);

      // Add timeout for sign out to prevent hanging
      const timeoutPromise = new Promise<{ error: { message: string } }>((resolve) => {
        setTimeout(() => {
          console.warn('Sign out timeout triggered after 5 seconds');
          resolve({ error: { message: 'Sign out timeout' } });
        }, 5000);
      });

      const signOutPromise = supabase.auth.signOut();
      const result = await Promise.race([signOutPromise, timeoutPromise]);

      if (result.error) {
        if (result.error.message === 'Sign out timeout') {
          console.error('Sign out timed out - forcing local sign out');
          toast.error('Sign out took longer than expected, but you have been signed out locally');
        } else {
          console.error('Sign out error:', result.error);
          toast.error('Error signing out - please try again');
        }
      } else {
        console.log('Sign out successful');
        toast.success('Signed out successfully');
      }
    } catch (error) {
      console.error('Exception during sign out:', error);
      toast.error('An unexpected error occurred during sign out');
    } finally {
      setLoading(false);
    }
  };

  // Refresh profile function - memoized to prevent infinite loops
  const refreshProfile = React.useCallback(async (): Promise<void> => {
    if (!user) {
      console.log('No user to refresh profile for');
      return;
    }

    console.log('Refreshing profile for user:', user.id);
    try {
      const profileData = await fetchProfile(user.id);
      if (profileData) {
        // Use startTransition for profile refresh to prevent suspension
        startTransition(() => {
          setProfile(profileData);
          setIsAdmin(isAdminRole(profileData.role));
        });
        console.log('Profile refreshed successfully');
      } else {
        console.warn('No profile data returned for user:', user.id);
      }
    } catch (error) {
      console.error('Error refreshing profile:', error);
    }
  }, [user?.id]); // Only depend on user ID

  // Create memoized context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => ({
    session,
    user,
    profile,
    loading,
    isAdmin,
    signIn,
    signUp,
    signOut,
    refreshProfile
  }), [session, user, profile, loading, isAdmin, refreshProfile]);

  // Provide the auth context to children
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default ConsolidatedAuthProvider;
