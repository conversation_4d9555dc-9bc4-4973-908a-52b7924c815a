import React from 'react';
import { motion } from 'framer-motion';
import { IoCalendar, IoLocationSharp, IoPeople } from 'react-icons/io5';
import type { Event } from '../../types';
import { glassmorphismClasses } from '@/lib/utils/styles';

interface EventCardProps {
  event: Event;
  onClick: () => void;
}

const EventCard: React.FC<EventCardProps> = ({ event, onClick }) => {
  return (
    <motion.div
      onClick={onClick}
      className={`${glassmorphismClasses.card} overflow-hidden rounded-xl cursor-pointer`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="relative">
        <div className="w-full h-48 bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
          <span className="text-white text-lg font-semibold">{event.title}</span>
        </div>
        <div className="absolute top-2 right-2 bg-purple-500 text-white text-xs px-2 py-1 rounded">
          Event
        </div>
      </div>

      <div className="p-4 space-y-3">
        <h3 className="text-lg font-semibold">{event.title}</h3>
        <p className="text-sm text-gray-400 line-clamp-2">{event.description}</p>

        <div className="flex items-center space-x-2 text-sm text-white/70">
          <IoCalendar className="flex-shrink-0" />
          <span>{event.start_date}</span>
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-white/70">
          <IoLocationSharp className="flex-shrink-0" />
          <span>{event.location}</span>
        </div>

        <div className="flex items-center space-x-2 text-sm text-white/70">
          <IoPeople className="flex-shrink-0" />
          <span>No attendees listed</span>
        </div>
      </div>
    </motion.div>
  );
};

export default EventCard;
